# SuperTokens Configuration Examples

# Copy this to your .env file and modify as needed

# ====================
# SuperTokens Core Settings
# ====================

# SuperTokens connection URI - use https://try.supertokens.io for development
SUPERTOKENS_CONNECTION_URI=https://try.supertokens.io

# Your application name
SUPERTOKENS_APP_NAME=OuiAssist

# API domain where your backend runs
SUPERTOKENS_API_DOMAIN=http://localhost:5000

# Website domain where your frontend runs
SUPERTOKENS_WEBSITE_DOMAIN=http://localhost:3000

# ====================
# Path Configuration
# ====================

# Base path for authentication API endpoints
SUPERTOKENS_API_BASE_PATH=/auth

# Base path for authentication UI (if using prebuilt UI)
SUPERTOKENS_WEBSITE_BASE_PATH=/auth

# ====================
# Dashboard Settings
# ====================

# Enable SuperTokens dashboard for user management
SUPERTOKENS_ENABLE_DASHBOARD=true

# Path where dashboard will be accessible
SUPERTOKENS_DASHBOARD_PATH=/dashboard

# ====================
# Email Verification
# ====================

# Email verification mode: REQUIRED, OPTIONAL, or OFF
SUPERTOKENS_EMAIL_VERIFICATION_MODE=REQUIRED

# ====================
# Production Settings (override for production)
# ====================

# For production, you would typically use:
# SUPERTOKENS_CONNECTION_URI=https://your-supertokens-instance.com
# SUPERTOKENS_API_DOMAIN=https://api.yourdomain.com
# SUPERTOKENS_WEBSITE_DOMAIN=https://yourdomain.com
# SUPERTOKENS_ENABLE_DASHBOARD=false  # Usually disabled in production

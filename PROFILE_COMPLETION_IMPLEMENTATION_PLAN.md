# User Profile Completion Flow Implementation Plan

## 🎯 Overview

This document outlines the comprehensive implementation plan for a user profile completion flow that integrates with SuperTokens authentication and uses Vuetify 3 for UI components.

### Key Requirements

- **Post-Signup Flow**: Redirect users to profile completion after successful signup
- **Profile Creation Logic**: Create full profile on form submission, minimal profile on skip
- **Returning User Flow**: Check profile completion status and redirect incomplete profiles
- **SuperTokens Integration**: Use `onHandleEvent` hook for post-auth redirection
- **UI Framework**: Leverage Vuetify 3 for form components and styling

### Architecture Alignment

- **Backend**: Flask with Clean Architecture pattern
- **Frontend**: Vue 3 + TypeScript with SuperTokens Web JS SDK
- **Domain Model**: Existing UserProfile with PersonName value object
- **Repository Pattern**: Interface-based with in-memory implementation

## 🏗️ Implementation Components

### 1. Backend Infrastructure Setup

#### 1.1 Use Cases to Create

**File: `core/use_cases/create_user_profile.py`**

```python
from datetime import datetime
from typing import Any, Dict
import uuid
from core.domain.user import PersonName, UserProfile
from core.repositories.interfaces.user_profile_repository import IUserProfileRepository

class CreateUserProfileUseCase:
    def __init__(self, profile_repository: IUserProfileRepository):
        self._profile_repository = profile_repository

    def execute(self, profile_data: Dict[str, Any]) -> UserProfile:
        # Create PersonName from form data
        name = PersonName(
            first_name=profile_data.get("first_name", ""),
            last_name=profile_data.get("last_name", ""),
            middle_name=profile_data.get("middle_name")
        )

        # Create UserProfile
        profile = UserProfile(
            id=str(uuid.uuid4()),
            authentication_id=profile_data["authentication_id"],
            name=name,
            phone_number=profile_data.get("phone_number"),
            avatar_url=profile_data.get("avatar_url"),
            bio=profile_data.get("bio"),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        return self._profile_repository.create_profile(profile)
```

**File: `core/use_cases/update_user_profile.py`**

```python
from datetime import datetime
from typing import Any, Dict
from core.domain.user import PersonName, UserProfile
from core.repositories.interfaces.user_profile_repository import IUserProfileRepository
from core.domain.exceptions import UserProfileError

class UpdateUserProfileUseCase:
    def __init__(self, profile_repository: IUserProfileRepository):
        self._profile_repository = profile_repository

    def execute(self, user_id: str, profile_data: Dict[str, Any]) -> UserProfile:
        existing_profile = self._profile_repository.get_profile_by_user_id(user_id)
        if not existing_profile:
            raise UserProfileError(f"Profile for user {user_id} not found")

        # Create updated PersonName
        name = PersonName(
            first_name=profile_data.get("first_name", existing_profile.name.first_name),
            last_name=profile_data.get("last_name", existing_profile.name.last_name),
            middle_name=profile_data.get("middle_name", existing_profile.name.middle_name)
        )

        # Create updated UserProfile
        updated_profile = UserProfile(
            id=existing_profile.id,
            authentication_id=existing_profile.authentication_id,
            name=name,
            phone_number=profile_data.get("phone_number", existing_profile.phone_number),
            avatar_url=profile_data.get("avatar_url", existing_profile.avatar_url),
            bio=profile_data.get("bio", existing_profile.bio),
            created_at=existing_profile.created_at,
            updated_at=datetime.utcnow()
        )

        return self._profile_repository.update_profile(updated_profile)
```

**File: `core/use_cases/create_minimal_profile.py`**

```python
from datetime import datetime
import uuid
from core.domain.user import PersonName, UserProfile
from core.repositories.interfaces.user_profile_repository import IUserProfileRepository

class CreateMinimalProfileUseCase:
    def __init__(self, profile_repository: IUserProfileRepository):
        self._profile_repository = profile_repository

    def execute(self, authentication_id: str) -> UserProfile:
        # Create minimal profile with placeholder data
        name = PersonName(
            first_name="User",
            last_name=f"#{authentication_id[:8]}",
            middle_name=None
        )

        profile = UserProfile(
            id=str(uuid.uuid4()),
            authentication_id=authentication_id,
            name=name,
            phone_number=None,
            avatar_url=None,
            bio=None,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        return self._profile_repository.create_profile(profile)
```

**File: `core/use_cases/check_profile_completion.py`**

```python
from typing import Dict, Any
from core.repositories.interfaces.user_profile_repository import IUserProfileRepository

class CheckProfileCompletionUseCase:
    def __init__(self, profile_repository: IUserProfileRepository):
        self._profile_repository = profile_repository

    def execute(self, user_id: str) -> Dict[str, Any]:
        profile = self._profile_repository.get_profile_by_user_id(user_id)

        if not profile:
            return {
                "exists": False,
                "is_complete": False,
                "is_minimal": False
            }

        # Check if profile is minimal (placeholder data)
        is_minimal = (
            profile.name.first_name == "User" and
            profile.name.last_name.startswith("#")
        )

        # Check if profile has essential information
        is_complete = (
            not is_minimal and
            profile.name.first_name.strip() != "" and
            profile.name.last_name.strip() != ""
        )

        return {
            "exists": True,
            "is_complete": is_complete,
            "is_minimal": is_minimal,
            "profile": {
                "first_name": profile.name.first_name,
                "last_name": profile.name.last_name,
                "middle_name": profile.name.middle_name,
                "phone_number": profile.phone_number,
                "avatar_url": profile.avatar_url,
                "bio": profile.bio
            }
        }
```

#### 1.2 API Endpoints

**File: `core/routes/app.py` - Add these endpoints**

```python
from flask import request
from core.repositories.user_profile_repository import InMemoryUserProfileRepository
from core.use_cases.create_user_profile import CreateUserProfileUseCase
from core.use_cases.update_user_profile import UpdateUserProfileUseCase
from core.use_cases.create_minimal_profile import CreateMinimalProfileUseCase
from core.use_cases.check_profile_completion import CheckProfileCompletionUseCase
from core.domain.exceptions import UserProfileError

@app.route("/profile", methods=["GET"])
@verify_session()
def get_user_profile():
    session_info = GetSessionInfoUseCase(SupertokensAuthRepository()).execute()
    profile_repo = InMemoryUserProfileRepository()

    try:
        profile_data = GetUserProfileUseCase(
            SupertokensAuthRepository(), profile_repo
        ).execute(session_info.user_id)
        return jsonify(profile_data)
    except UserNotFoundError:
        return jsonify({"error": "User not found"}), 404

@app.route("/profile", methods=["POST"])
@verify_session()
def create_user_profile():
    session_info = GetSessionInfoUseCase(SupertokensAuthRepository()).execute()
    profile_repo = InMemoryUserProfileRepository()

    data = request.get_json()
    data["authentication_id"] = session_info.user_id

    try:
        profile = CreateUserProfileUseCase(profile_repo).execute(data)
        return jsonify({"status": "success", "profile_id": profile.id})
    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route("/profile", methods=["PUT"])
@verify_session()
def update_user_profile():
    session_info = GetSessionInfoUseCase(SupertokensAuthRepository()).execute()
    profile_repo = InMemoryUserProfileRepository()

    data = request.get_json()

    try:
        profile = UpdateUserProfileUseCase(profile_repo).execute(session_info.user_id, data)
        return jsonify({"status": "success", "profile_id": profile.id})
    except UserProfileError as e:
        return jsonify({"error": str(e)}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route("/profile/check", methods=["GET"])
@verify_session()
def check_profile_completion():
    session_info = GetSessionInfoUseCase(SupertokensAuthRepository()).execute()
    profile_repo = InMemoryUserProfileRepository()

    try:
        result = CheckProfileCompletionUseCase(profile_repo).execute(session_info.user_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route("/profile/minimal", methods=["POST"])
@verify_session()
def create_minimal_profile():
    session_info = GetSessionInfoUseCase(SupertokensAuthRepository()).execute()
    profile_repo = InMemoryUserProfileRepository()

    try:
        profile = CreateMinimalProfileUseCase(profile_repo).execute(session_info.user_id)
        return jsonify({"status": "success", "profile_id": profile.id})
    except Exception as e:
        return jsonify({"error": str(e)}), 400
```

### 2. Frontend Infrastructure Setup

#### 2.1 Vuetify 3 Installation

**Commands to run:**

```powershell
cd ouiassist/frontend
pnpm add vuetify@^3.0.0 @mdi/font
```

#### 2.2 Vuetify Configuration

**File: `ouiassist/frontend/src/plugins/vuetify.ts`**

```typescript
import { createVuetify } from "vuetify";
import * as components from "vuetify/components";
import * as directives from "vuetify/directives";
import { mdi } from "vuetify/iconsets/mdi";
import "@mdi/font/css/materialdesignicons.css";
import "vuetify/styles";

export default createVuetify({
  components,
  directives,
  icons: {
    defaultSet: "mdi",
    sets: { mdi },
  },
  theme: {
    defaultTheme: "light",
    themes: {
      light: {
        colors: {
          primary: "#1976D2",
          secondary: "#424242",
          accent: "#82B1FF",
          error: "#FF5252",
          info: "#2196F3",
          success: "#4CAF50",
          warning: "#FFC107",
        },
      },
    },
  },
});
```

**File: `ouiassist/frontend/src/main.ts` - Update to include Vuetify**

```typescript
import { createApp } from "vue";
import "./style.css";
import App from "./App.vue";
import router from "./router";
import { initSuperTokensWebJS } from "./config";
import vuetify from "./plugins/vuetify";

initSuperTokensWebJS();
createApp(App).use(router).use(vuetify).mount("#app");
```

#### 2.3 Profile Completion Components

**File: `ouiassist/frontend/src/views/ProfileCompletionView.vue`**

```vue
<template>
  <v-app>
    <v-container class="fill-height" fluid>
      <v-row justify="center" align="center">
        <v-col cols="12" sm="8" md="6" lg="4">
          <v-card class="elevation-12">
            <v-card-title
              class="text-h4 text-center pa-6 bg-primary text-white"
            >
              {{ isUpdate ? "Update Your Profile" : "Complete Your Profile" }}
            </v-card-title>
            <v-card-text class="pa-6">
              <ProfileForm
                :initial-data="profileData"
                :is-update="isUpdate"
                :loading="loading"
                @submit="handleSubmit"
                @skip="handleSkip"
              />
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </v-app>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import ProfileForm from "../components/ProfileForm.vue";
import { getApiDomain } from "../config";

const route = useRoute();
const router = useRouter();

const isUpdate = ref(false);
const profileData = ref({});
const loading = ref(false);

onMounted(async () => {
  const mode = route.query.mode as string;
  isUpdate.value = mode === "update";

  if (isUpdate.value) {
    await loadExistingProfile();
  }
});

async function loadExistingProfile() {
  try {
    const response = await fetch(`${getApiDomain()}/profile/check`, {
      credentials: "include",
    });

    if (response.ok) {
      const data = await response.json();
      if (data.exists && data.profile) {
        profileData.value = data.profile;
      }
    }
  } catch (error) {
    console.error("Error loading profile:", error);
  }
}

async function handleSubmit(formData: any) {
  loading.value = true;

  try {
    const endpoint = isUpdate.value ? "/profile" : "/profile";
    const method = isUpdate.value ? "PUT" : "POST";

    const response = await fetch(`${getApiDomain()}${endpoint}`, {
      method,
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify(formData),
    });

    if (response.ok) {
      router.push("/dashboard");
    } else {
      const error = await response.json();
      console.error("Profile submission error:", error);
    }
  } catch (error) {
    console.error("Network error:", error);
  } finally {
    loading.value = false;
  }
}

async function handleSkip() {
  if (!isUpdate.value) {
    loading.value = true;

    try {
      const response = await fetch(`${getApiDomain()}/profile/minimal`, {
        method: "POST",
        credentials: "include",
      });

      if (response.ok) {
        router.push("/dashboard");
      }
    } catch (error) {
      console.error("Error creating minimal profile:", error);
    } finally {
      loading.value = false;
    }
  }
}
</script>
```

**File: `ouiassist/frontend/src/components/ProfileForm.vue`**

```vue
<template>
  <v-form ref="form" v-model="valid" @submit.prevent="onSubmit">
    <v-row>
      <v-col cols="12" sm="6">
        <v-text-field
          v-model="formData.first_name"
          label="First Name"
          :rules="nameRules"
          required
          variant="outlined"
          prepend-inner-icon="mdi-account"
        />
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          v-model="formData.last_name"
          label="Last Name"
          :rules="nameRules"
          required
          variant="outlined"
        />
      </v-col>
    </v-row>

    <v-text-field
      v-model="formData.middle_name"
      label="Middle Name (Optional)"
      variant="outlined"
      class="mb-3"
    />

    <v-text-field
      v-model="formData.phone_number"
      label="Phone Number (Optional)"
      :rules="phoneRules"
      variant="outlined"
      prepend-inner-icon="mdi-phone"
      class="mb-3"
    />

    <v-textarea
      v-model="formData.bio"
      label="Bio (Optional)"
      variant="outlined"
      prepend-inner-icon="mdi-text"
      rows="3"
      class="mb-3"
    />

    <v-text-field
      v-model="formData.avatar_url"
      label="Avatar URL (Optional)"
      variant="outlined"
      prepend-inner-icon="mdi-image"
      class="mb-4"
    />

    <v-row>
      <v-col cols="12" sm="6">
        <v-btn
          type="submit"
          color="primary"
          size="large"
          block
          :loading="loading"
          :disabled="!valid"
        >
          {{ isUpdate ? "Update Profile" : "Complete Profile" }}
        </v-btn>
      </v-col>
      <v-col cols="12" sm="6" v-if="!isUpdate">
        <v-btn
          color="secondary"
          variant="outlined"
          size="large"
          block
          :loading="loading"
          @click="onSkip"
        >
          Skip for Now
        </v-btn>
      </v-col>
    </v-row>
  </v-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";

interface Props {
  initialData?: any;
  isUpdate?: boolean;
  loading?: boolean;
}

interface Emits {
  (e: "submit", data: any): void;
  (e: "skip"): void;
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
  isUpdate: false,
  loading: false,
});

const emit = defineEmits<Emits>();

const form = ref();
const valid = ref(false);

const formData = reactive({
  first_name: "",
  last_name: "",
  middle_name: "",
  phone_number: "",
  bio: "",
  avatar_url: "",
});

const nameRules = [
  (v: string) => !!v || "This field is required",
  (v: string) => v.length >= 2 || "Must be at least 2 characters",
];

const phoneRules = [
  (v: string) =>
    !v || /^[\+]?[1-9][\d]{0,15}$/.test(v) || "Invalid phone number format",
];

// Watch for initial data changes
watch(
  () => props.initialData,
  (newData) => {
    if (newData) {
      Object.assign(formData, newData);
    }
  },
  { immediate: true }
);

function onSubmit() {
  if (valid.value) {
    emit("submit", { ...formData });
  }
}

function onSkip() {
  emit("skip");
}
</script>
```

### 3. SuperTokens Integration

#### 3.1 Profile Utility Functions

**File: `ouiassist/frontend/src/utils/profileUtils.ts`**

```typescript
import { getApiDomain } from "../config";

export async function checkProfileCompletion(): Promise<{
  exists: boolean;
  isComplete: boolean;
  isMinimal: boolean;
  profile?: any;
}> {
  try {
    const response = await fetch(`${getApiDomain()}/profile/check`, {
      credentials: "include",
    });

    if (response.ok) {
      return await response.json();
    }

    return {
      exists: false,
      isComplete: false,
      isMinimal: false,
    };
  } catch (error) {
    console.error("Error checking profile completion:", error);
    return {
      exists: false,
      isComplete: false,
      isMinimal: false,
    };
  }
}

export function getProfileCompletionRedirectUrl(profileStatus: {
  exists: boolean;
  isComplete: boolean;
  isMinimal: boolean;
}): string {
  if (!profileStatus.exists) {
    return "/profile-completion?mode=create";
  }

  if (profileStatus.isMinimal || !profileStatus.isComplete) {
    return "/profile-completion?mode=update";
  }

  return "/dashboard";
}
```

#### 3.2 Updated SuperTokens Configuration

**File: `ouiassist/frontend/src/config.ts` - Update with event handling**

```typescript
import SuperTokens from "supertokens-web-js";
import Session from "supertokens-web-js/recipe/session";
import {
  checkProfileCompletion,
  getProfileCompletionRedirectUrl,
} from "./utils/profileUtils";

const isMultitenancy = false;

export function getApiDomain() {
  const apiPort = 3001;
  const apiUrl = `http://localhost:${apiPort}`;
  return apiUrl;
}

export function getWebsiteDomain() {
  const websitePort = 3000;
  const websiteUrl = `http://localhost:${websitePort}`;
  return websiteUrl;
}

export function initSuperTokensUI() {
  (window as any).supertokensUIInit("supertokensui", {
    appInfo: {
      websiteDomain: getWebsiteDomain(),
      apiDomain: getApiDomain(),
      appName: "SuperTokens Demo App",
      websiteBasePath: "/auth",
      apiBasePath: "/auth",
    },

    recipeList: [
      (window as any).supertokensUISession.init(),
      (window as any).supertokensUIEmailPassword.init(),
    ],

    getRedirectionURL: async (context: any) => {
      if (context.action === "SUCCESS") {
        // Check profile completion status and redirect accordingly
        const profileStatus = await checkProfileCompletion();
        return getProfileCompletionRedirectUrl(profileStatus);
      }
      return undefined;
    },

    onHandleEvent: async (context: any) => {
      if (context.action === "SUCCESS") {
        console.log("Auth success event:", context);

        // Small delay to ensure session is properly established
        setTimeout(async () => {
          try {
            if (context.isNewUser) {
              // New user - always redirect to profile completion
              window.location.href = "/profile-completion?mode=create";
            } else {
              // Existing user - check profile completion status
              const profileStatus = await checkProfileCompletion();
              const redirectUrl =
                getProfileCompletionRedirectUrl(profileStatus);

              if (redirectUrl !== "/dashboard") {
                window.location.href = redirectUrl;
              }
            }
          } catch (error) {
            console.error("Error in onHandleEvent:", error);
            // Fallback to dashboard on error
            window.location.href = "/dashboard";
          }
        }, 100);
      }
    },
  });
}

export function initSuperTokensWebJS() {
  SuperTokens.init({
    appInfo: {
      appName: "SuperTokens Demo App",
      apiDomain: getApiDomain(),
      apiBasePath: "/auth",
    },
    recipeList: [Session.init()],
  });

  if (isMultitenancy) {
    initTenantSelectorInterface();
  }
}

// Keep existing tenant selector function if it exists
function initTenantSelectorInterface() {
  // Existing implementation
}
```

#### 3.3 Router Updates

**File: `ouiassist/frontend/src/router/index.ts` - Add profile completion route**

```typescript
import { createRouter, createWebHistory } from "vue-router";
import HomeView from "../views/HomeView.vue";
import AuthView from "../views/AuthView.vue";
import DashboardView from "../views/DashboardView.vue";
import ProfileCompletionView from "../views/ProfileCompletionView.vue";
import Session from "supertokens-web-js/recipe/session";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "home",
      component: HomeView,
    },
    {
      path: "/auth/:pathMatch(.*)*",
      name: "auth",
      component: AuthView,
    },
    {
      path: "/profile-completion",
      name: "profile-completion",
      component: ProfileCompletionView,
      beforeEnter: async (to, from, next) => {
        if (!(await Session.doesSessionExist())) {
          next("/auth");
        } else {
          next();
        }
      },
    },
    {
      path: "/dashboard",
      name: "dashboard",
      component: DashboardView,
      beforeEnter: async (_, _1, next) => {
        if (!(await Session.doesSessionExist())) {
          next("/auth");
        } else {
          next();
        }
      },
    },
  ],
});

export default router;
```

## 🔄 User Flow Diagrams

### New User Signup Flow

```mermaid
flowchart TD
    A[User visits /auth] --> B[User signs up]
    B --> C[SuperTokens SUCCESS event]
    C --> D{isNewUser?}
    D -->|Yes| E[Redirect to /profile-completion?mode=create]
    E --> F[User sees profile form]
    F --> G{User action}
    G -->|Submits form| H[POST /profile with full data]
    G -->|Clicks skip| I[POST /profile/minimal]
    H --> J[Redirect to /dashboard]
    I --> J
    D -->|No| K[Check profile completion]
    K --> L{Profile complete?}
    L -->|Yes| J
    L -->|No| M[Redirect to /profile-completion?mode=update]
    M --> F
```

### Returning User Signin Flow

```mermaid
flowchart TD
    A[User visits /auth] --> B[User signs in]
    B --> C[SuperTokens SUCCESS event]
    C --> D[onHandleEvent triggered]
    D --> E{isNewUser?}
    E -->|No| F[Call /profile/check API]
    F --> G{Profile exists?}
    G -->|No| H[Redirect to /profile-completion?mode=create]
    G -->|Yes| I{Profile complete?}
    I -->|Yes| J[Redirect to /dashboard]
    I -->|No| K{Profile minimal?}
    K -->|Yes| L[Redirect to /profile-completion?mode=update]
    K -->|No| L
    H --> M[Show profile form]
    L --> M
    M --> N{User submits?}
    N -->|Yes| O[PUT /profile with updated data]
    N -->|Skip| P[Keep existing profile]
    O --> J
    P --> J
    E -->|Yes| Q[New user flow - see above diagram]
```

### Profile Completion Decision Tree

```mermaid
flowchart TD
    A[Check Profile Status] --> B{Profile exists?}
    B -->|No| C[mode=create]
    B -->|Yes| D{Is minimal profile?}
    D -->|Yes| E[mode=update]
    D -->|No| F{Has required fields?}
    F -->|No| E
    F -->|Yes| G[Redirect to dashboard]
    C --> H[Show form with empty fields]
    E --> I[Show form with existing data]
    H --> J[Allow skip → create minimal]
    I --> K[No skip option]
    J --> L[Submit or Skip]
    K --> M[Submit only]
    L --> N{Action}
    N -->|Submit| O[Create full profile]
    N -->|Skip| P[Create minimal profile]
    M --> O
    O --> Q[Go to dashboard]
    P --> Q
```

## 📋 API Endpoint Specifications

| Method | Endpoint           | Purpose                  | Request Body                                                                | Response                                                                        |
| ------ | ------------------ | ------------------------ | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------- |
| GET    | `/profile`         | Get user profile         | None                                                                        | `{supertokens_data: {...}, profile: {...}}`                                     |
| POST   | `/profile`         | Create new profile       | `{first_name, last_name, middle_name?, phone_number?, bio?, avatar_url?}`   | `{status: "success", profile_id: string}`                                       |
| PUT    | `/profile`         | Update existing profile  | `{first_name?, last_name?, middle_name?, phone_number?, bio?, avatar_url?}` | `{status: "success", profile_id: string}`                                       |
| GET    | `/profile/check`   | Check profile completion | None                                                                        | `{exists: boolean, is_complete: boolean, is_minimal: boolean, profile?: {...}}` |
| POST   | `/profile/minimal` | Create minimal profile   | None                                                                        | `{status: "success", profile_id: string}`                                       |

## 📅 Implementation Timeline & Task Dependencies

### Phase 1: Backend Foundation (Days 1-2)

1. **Create Use Cases** (Day 1)

   - `CreateUserProfileUseCase`
   - `UpdateUserProfileUseCase`
   - `CreateMinimalProfileUseCase`
   - `CheckProfileCompletionUseCase`

2. **Add API Endpoints** (Day 2)
   - Update `core/routes/app.py` with all profile endpoints
   - Test endpoints with Postman/curl

### Phase 2: Frontend Infrastructure (Days 3-4)

3. **Install & Configure Vuetify** (Day 3)

   - Install Vuetify 3 and dependencies
   - Create Vuetify plugin configuration
   - Update main.ts to include Vuetify

4. **Create Profile Components** (Day 4)
   - `ProfileCompletionView.vue`
   - `ProfileForm.vue`
   - Add route to router configuration

### Phase 3: SuperTokens Integration (Day 5)

5. **Profile Utilities & SuperTokens Config**
   - Create `profileUtils.ts` helper functions
   - Update SuperTokens configuration with event handlers
   - Test authentication flow integration

### Phase 4: Testing & Refinement (Days 6-7)

6. **End-to-End Testing**

   - Test new user signup → profile completion flow
   - Test returning user signin → profile check flow
   - Test form validation and error handling
   - Test skip functionality and minimal profile creation

7. **Polish & Edge Cases**
   - Loading states and error messages
   - Form validation feedback
   - Mobile responsiveness
   - Accessibility improvements

## 🔧 Implementation Order

**Critical Path Dependencies:**

1. Backend Use Cases → API Endpoints
2. Vuetify Installation → Profile Components
3. Profile Components → SuperTokens Integration
4. All Components → Testing

**Parallel Work Opportunities:**

- Backend development (Phase 1) can proceed independently
- Frontend infrastructure (Vuetify setup) can start before backend completion
- Component development can begin once Vuetify is configured

## 🎯 Success Criteria

### Functional Requirements

- ✅ New users redirected to profile completion after signup
- ✅ Existing users with incomplete profiles redirected to profile update
- ✅ Users can submit complete profile data or skip with minimal profile
- ✅ Form validation prevents invalid data submission
- ✅ Successful profile creation/update redirects to dashboard

### Technical Requirements

- ✅ Clean Architecture patterns maintained
- ✅ SuperTokens integration working seamlessly
- ✅ Vuetify components properly styled and responsive
- ✅ TypeScript types properly defined
- ✅ Error handling for network failures and validation errors

### User Experience Requirements

- ✅ Intuitive form layout with clear field labels
- ✅ Loading states during API operations
- ✅ Clear distinction between required and optional fields
- ✅ Helpful validation messages
- ✅ Smooth navigation flow without jarring redirects

## 🚀 Next Steps

1. **Create the planning document** ✅ (This document)
2. **Begin Backend Implementation** - Start with use cases
3. **Install Frontend Dependencies** - Vuetify 3 setup
4. **Implement Components** - Profile forms and views
5. **Configure SuperTokens** - Event handling integration
6. **Test & Refine** - End-to-end flow validation

---

_This implementation plan serves as the comprehensive guide for building the user profile completion flow. Reference this document throughout development to ensure consistency and completeness._

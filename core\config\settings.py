from supertokens_python import InputAppInfo, SupertokensConfig, init
from supertokens_python.recipe import dashboard, emailpassword, session, userroles


def get_api_domain() -> str:
    api_port = str(3001)
    api_url = f"http://localhost:{api_port}"
    return api_url


def get_website_domain() -> str:
    website_port = str(3000)
    website_url = f"http://localhost:{website_port}"
    return website_url


def initialize_supertokens() -> None:
    supertokens_config = SupertokensConfig(connection_uri="http://localhost:3567/")

    app_info = InputAppInfo(
        app_name="SuperTokens Demo App",
        api_domain=get_api_domain(),
        website_domain=get_website_domain(),
        api_base_path="/auth",
        website_base_path="/auth",
    )

    recipe_list = [
        session.init(),
        dashboard.init(),
        userroles.init(),
        emailpassword.init(),
    ]

    init(
        supertokens_config=supertokens_config,
        app_info=app_info,
        framework="flask",  # Changed from "fastapi" to "flask"
        recipe_list=recipe_list,
        mode="wsgi",  # Changed from "asgi" to "wsgi" for Flask
        telemetry=False,
    )

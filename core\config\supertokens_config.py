import os
from dataclasses import dataclass
from typing import Any, Dict, Optional

from supertokens_python import InputAppInfo, SupertokensConfig, init
from supertokens_python.recipe import (
    dashboard,
    emailpassword,
    emailverification,
    session,
    usermetadata,
)


@dataclass
class SuperTokensSettings:
    """SuperTokens configuration settings"""

    connection_uri: str
    app_name: str
    api_domain: str
    website_domain: str
    api_base_path: str = "/auth"
    website_base_path: str = "/auth"
    enable_dashboard: bool = True
    dashboard_path: str = "/dashboard"
    email_verification_mode: str = "REQUIRED"

    @classmethod
    def from_env(cls) -> "SuperTokensSettings":
        """Create settings from environment variables"""
        return cls(
            connection_uri=os.getenv(
                "SUPERTOKENS_CONNECTION_URI", "https://try.supertokens.io"
            ),
            app_name=os.getenv("SUPERTOKENS_APP_NAME", "OuiAssist"),
            api_domain=os.getenv("SUPERTOKENS_API_DOMAIN", "http://localhost:5000"),
            website_domain=os.getenv(
                "SUPERTOKENS_WEBSITE_DOMAIN", "http://localhost:3000"
            ),
            api_base_path=os.getenv("SUPERTOKENS_API_BASE_PATH", "/auth"),
            website_base_path=os.getenv("SUPERTOKENS_WEBSITE_BASE_PATH", "/auth"),
            enable_dashboard=os.getenv("SUPERTOKENS_ENABLE_DASHBOARD", "true").lower()
            == "true",
            dashboard_path=os.getenv("SUPERTOKENS_DASHBOARD_PATH", "/dashboard"),
            email_verification_mode=os.getenv(
                "SUPERTOKENS_EMAIL_VERIFICATION_MODE", "REQUIRED"
            ),
        )


class SuperTokensConfig:
    """SuperTokens configuration and initialization"""

    def __init__(self, settings: SuperTokensSettings):
        self.settings = settings

    def initialize(self) -> None:
        """Initialize SuperTokens with configuration"""
        recipe_list = [
            session.init(),
            emailpassword.init(),
            emailverification.init(self.settings.email_verification_mode),
            usermetadata.init(),
        ]

        # Add dashboard recipe if enabled
        if self.settings.enable_dashboard:
            recipe_list.append(dashboard.init())

        init(
            supertokens_config=SupertokensConfig(
                connection_uri=self.settings.connection_uri
            ),
            app_info=InputAppInfo(
                app_name=self.settings.app_name,
                api_domain=self.settings.api_domain,
                website_domain=self.settings.website_domain,
                api_base_path=self.settings.api_base_path,
                website_base_path=self.settings.website_base_path,
            ),
            framework="flask",
            recipe_list=recipe_list,
            telemetry=False,
        )

    def get_cors_config(self) -> Dict[str, Any]:
        """Get CORS configuration for Flask app"""
        from supertokens_python import get_all_cors_headers

        return {
            "supports_credentials": True,
            "origins": [self.settings.website_domain],
            "allow_headers": ["Content-Type"] + get_all_cors_headers(),
        }

    @classmethod
    def create_from_env(cls) -> "SuperTokensConfig":
        """Create configuration from environment variables"""
        settings = SuperTokensSettings.from_env()
        return cls(settings)


def initialize_supertokens(
    settings: Optional[SuperTokensSettings] = None,
) -> SuperTokensConfig:
    """
    Initialize SuperTokens with the provided settings or from environment.

    Args:
        settings: Optional SuperTokens settings. If None, will load from environment.

    Returns:
        SuperTokensConfig: Initialized configuration instance
    """
    if settings is None:
        settings = SuperTokensSettings.from_env()

    config = SuperTokensConfig(settings)
    config.initialize()
    return config

from dataclasses import dataclass
from typing import Any, Dict, List


@dataclass
class SessionInfo:
    session_handle: str
    user_id: str
    access_token_payload: Dict[str, Any]

    def __post_init__(self) -> None:
        if not self.session_handle:
            raise ValueError("Session handle cannot be empty")
        if not self.user_id:
            raise ValueError("User ID cannot be empty")

    def to_dict(self) -> Dict[str, Any]:
        return {
            "session_handle": self.session_handle,
            "user_id": self.user_id,
            "access_token_payload": self.access_token_payload,
        }


@dataclass
class Tenant:
    tenant_id: str
    config: Dict[str, Any]
    email_password_enabled: bool
    third_party_enabled: bool
    passwordless_enabled: bool

    @classmethod
    def from_supertokens_json(cls, tenant_json: Dict[str, Any]) -> "Tenant":
        return cls(
            tenant_id=tenant_json.get("tenantId", ""),
            config=tenant_json,
            email_password_enabled=tenant_json.get("emailPassword", {}).get(
                "enabled", False
            ),
            third_party_enabled=tenant_json.get("thirdParty", {}).get("enabled", False),
            passwordless_enabled=tenant_json.get("passwordless", {}).get(
                "enabled", False
            ),
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "tenantId": self.tenant_id,
            "config": self.config,
            "emailPassword": {"enabled": self.email_password_enabled},
            "thirdParty": {"enabled": self.third_party_enabled},
            "passwordless": {"enabled": self.passwordless_enabled},
        }


@dataclass
class TenantList:
    status: str
    tenants: List[Tenant]

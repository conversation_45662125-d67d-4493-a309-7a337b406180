from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass(frozen=True, eq=True)
class PersonName:
    first_name: str
    last_name: str
    middle_name: Optional[str]

    def __post_init__(self) -> None:
        if not self.first_name.strip():
            raise ValueError("First name cannot be empty")
        if not self.last_name.strip():
            raise ValueError("Last name cannot be empty")
        if self.middle_name is not None and not self.middle_name.strip():
            raise ValueError("Middle name cannot be empty if provided")

    @property
    def full_name(self) -> str:
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"


@dataclass(frozen=True, eq=True)
class UserProfile:
    id: str
    authentication_id: str  # Reference to authentication system user (SuperTokens)
    name: PersonName
    phone_number: Optional[str]
    avatar_url: Optional[str]
    bio: Optional[str]
    created_at: datetime
    updated_at: datetime

    def __post_init__(self) -> None:
        if not self.id:
            raise ValueError("Profile ID cannot be empty")
        if not self.authentication_id:
            raise ValueError("Authentication ID cannot be empty")


@dataclass(frozen=True, eq=True)
class UserIdentity:
    user_id: str
    email: Optional[str]
    phone_number: Optional[str]
    time_joined: Optional[int]

    def __post_init__(self) -> None:
        if not self.user_id:
            raise ValueError("User ID cannot be empty")

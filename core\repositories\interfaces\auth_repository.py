from typing import Optional, Protocol

from core.domain.authentication import <PERSON><PERSON>n<PERSON>, TenantList
from core.domain.user import UserIdentity


class IAuthRepository(Protocol):
    def get_current_session_info(self) -> SessionInfo: ...

    def get_all_tenants(self) -> TenantList: ...

    def get_user_by_id(self, user_id: str) -> Optional[UserIdentity]: ...

    def get_user_by_email(
        self, email: str, tenant_id: str = "public"
    ) -> Optional[UserIdentity]: ...

    def delete_user(self, user_id: str) -> Optional[bool]: ...

from typing import List, Optional, Protocol

from core.domain.user import UserProfile


class IUserProfileRepository(Protocol):
    def get_profile_by_user_id(self, user_id: str) -> Optional[UserProfile]: ...

    def create_profile(self, profile: UserProfile) -> UserProfile: ...

    def update_profile(self, profile: UserProfile) -> UserProfile: ...

    def delete_profile(self, user_id: str) -> bool: ...

    def search_profiles(self, query: str, limit: int = 10) -> List[UserProfile]: ...

from typing import Optional

from flask import g
from supertokens_python.recipe.multitenancy.syncio import list_all_tenants
from supertokens_python.types import AccountInfo
from supertokens_python.syncio import delete_user, get_user, list_users_by_account_info

from core.domain.authentication import <PERSON><PERSON><PERSON><PERSON>, Tenant, TenantList
from core.domain.exceptions import SessionNotFoundError, UserNotFoundError
from core.repositories.interfaces.auth_repository import IAuthRepository
from core.domain.user import UserIdentity


class SupertokensAuthRepository(IAuthRepository):
    def get_current_session_info(self) -> SessionInfo:
        try:
            session = g.supertokens
            return SessionInfo(
                session_handle=session.get_handle(),
                user_id=session.get_user_id(),
                access_token_payload=session.get_access_token_payload(),
            )
        except AttributeError as e:
            raise SessionNotFoundError("No active session found") from e

    def get_all_tenants(self) -> TenantList:
        tenant_response = list_all_tenants()

        tenants = [
            Tenant.from_supertokens_json(tenant.to_json())
            for tenant in tenant_response.tenants
        ]

        return TenantList(status="OK", tenants=tenants)

    def get_user_by_id(self, user_id: str) -> Optional[UserIdentity]:
        try:
            user_response = get_user(user_id)
            if user_response is None:
                return None
            email = user_response.email
            phone_number = user_response.phone_number

            return UserIdentity(
                user_id=user_response.user_id,
                email=email,
                phone_number=phone_number,
                time_joined=user_response.time_joined,
            )

        except Exception as e:
            raise UserNotFoundError(f"User {user_id} not found") from e

    def get_user_by_email(
        self, email: str, tenant_id: str = "public"
    ) -> Optional[UserIdentity]:
        try:
            users_response = list_users_by_account_info(
                tenant_id=tenant_id,
                account_info=AccountInfo(email=email),
            )

            if not users_response or len(users_response) == 0:
                return None

            user = users_response[0]
            return self.get_user_by_id(user.user_id)

        except Exception as e:
            raise UserNotFoundError(f"User with email {email} not found") from e

    def delete_user(self, user_id: str) -> Optional[bool]:
        try:
            delete_user(user_id)
            if self.get_user_by_id(user_id) is not None:
                return False
            return True
        except Exception:
            raise UserNotFoundError(f"User {user_id} not found or could not be deleted")

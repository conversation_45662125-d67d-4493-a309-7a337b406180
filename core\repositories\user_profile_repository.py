from datetime import datetime
from typing import Dict, List, Optional

from core.domain.exceptions import UserProfileError
from core.domain.user import UserProfile
from core.repositories.interfaces.user_profile_repository import IUserProfileRepository


class InMemoryUserProfileRepository(IUserProfileRepository):
    """In-memory user profile repository implementation.

    In a real application, this would connect to a database like PostgreSQL, MongoDB, etc.
    """

    def __init__(self) -> None:
        self._profiles: Dict[str, UserProfile] = {}

    def get_profile_by_user_id(self, user_id: str) -> Optional[UserProfile]:
        return self._profiles.get(user_id)

    def create_profile(self, profile: UserProfile) -> UserProfile:
        if profile.id in self._profiles:
            raise UserProfileError(f"Profile for user {profile.id} already exists")
        self._profiles[profile.id] = profile
        return profile

    def update_profile(self, profile: UserProfile) -> UserProfile:
        if profile.id not in self._profiles:
            raise UserProfileError(f"Profile for user {profile.id} not found")
        self._profiles[profile.id] = profile
        return profile

    def delete_profile(self, user_id: str) -> bool:
        if user_id in self._profiles:
            del self._profiles[user_id]
            return True
        return False

    def search_profiles(self, query: str, limit: int = 10) -> List[UserProfile]:
        query_lower = query.lower()
        results = []

        for profile in self._profiles.values():
            if (
                profile.name.full_name and query_lower in profile.name.full_name.lower()
            ) or (profile.bio and query_lower in profile.bio.lower()):
                results.append(profile)
                if len(results) >= limit:
                    break

        return results


# For production, you would implement a database-backed repository like this:
"""
class DatabaseUserProfileRepository(IUserProfileRepository):
    def __init__(self, db_connection):
        self.db = db_connection

    def get_profile_by_user_id(self, user_id: str) -> Optional[UserProfile]:
        # SQL query to fetch profile
        pass

    # ... other methods
"""

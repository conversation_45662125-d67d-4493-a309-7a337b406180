from flask import Flask, abort, g, jsonify
from flask_cors import CORS
from supertokens_python import get_all_cors_headers
from supertokens_python.framework.flask import Middleware
from supertokens_python.recipe.session.framework.flask import verify_session

from core.config.settings import initialize_supertokens
from core.repositories.supertokens_auth_repository import SupertokensAuthRepository
from core.use_cases.get_session_info import GetSessionInfoUseCase
from core.use_cases.list_tenants import ListTenantsUseCase

app = Flask(__name__)
# Make Flask handle trailing slashes consistently
app.url_map.strict_slashes = False

# TODO: should middlware be after or before cors?
initialize_supertokens()
Middleware(app)
CORS(
    app=app,
    supports_credentials=True,
    origins="http://localhost:3000",
    allow_headers=["Content-Type"] + get_all_cors_headers(),
)


@app.route("/sessioninfo", methods=["GET"])  # type: ignore
@verify_session()
def get_session_info():
    repo = SupertokensAuthRepository()
    session_info = GetSessionInfoUseCase(repo).execute()
    return jsonify(
        session_info.to_dict()
        if session_info
        else {"status": "No active session found"}
    )


@app.route("/tenants", methods=["GET"])  # type: ignore
def get_tenants():
    repo = SupertokensAuthRepository()
    tenantResponse = ListTenantsUseCase(repo).execute()

    tenantsList = []

    for tenant in tenantResponse.tenants:
        tenantsList.append(tenant.to_json())

    return jsonify(
        {
            "status": "OK",
            "tenants": tenantsList,
        }
    )


# This is required since if this is not there, then OPTIONS requests for
# the APIs exposed by the supertokens' Middleware will return a 404
@app.route("/", defaults={"u_path": ""})  # type: ignore
@app.route("/<path:u_path>")  # type: ignore
def catch_all(u_path: str):  # pylint: disable=unused-argument
    abort(404)


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=int("3001"), debug=True)

from flask import Flask, abort, g, jsonify, request
from flask_cors import CORS
from supertokens_python import get_all_cors_headers
from supertokens_python.framework.flask import Middleware
from supertokens_python.recipe.session.framework.flask import verify_session

from core.config.settings import initialize_supertokens
from core.repositories.supertokens_auth_repository import SupertokensAuthRepository
from core.repositories.user_profile_repository import InMemoryUserProfileRepository
from core.use_cases.get_session_info import GetSessionInfoUseCase
from core.use_cases.list_tenants import ListTenantsUseCase
from core.use_cases.get_user_profile import GetUserProfileUseCase
from core.use_cases.create_user_profile import CreateUserProfileUseCase
from core.use_cases.update_user_profile import UpdateUserProfileUseCase
from core.use_cases.create_minimal_profile import CreateMinimalProfileUseCase
from core.use_cases.check_profile_completion import CheckProfileCompletionUseCase
from core.domain.exceptions import UserProfileError, UserNotFoundError

app = Flask(__name__)
# Make Flask handle trailing slashes consistently
app.url_map.strict_slashes = False

# TODO: should middlware be after or before cors?
initialize_supertokens()
Middleware(app)
CORS(
    app=app,
    supports_credentials=True,
    origins="http://localhost:3000",
    allow_headers=["Content-Type"] + get_all_cors_headers(),
)


@app.route("/sessioninfo", methods=["GET"])  # type: ignore
@verify_session()
def get_session_info():
    repo = SupertokensAuthRepository()
    session_info = GetSessionInfoUseCase(repo).execute()
    return jsonify(
        session_info.to_dict()
        if session_info
        else {"status": "No active session found"}
    )


@app.route("/tenants", methods=["GET"])  # type: ignore
def get_tenants():
    repo = SupertokensAuthRepository()
    tenantResponse = ListTenantsUseCase(repo).execute()

    tenantsList = []

    for tenant in tenantResponse.tenants:
        tenantsList.append(tenant.to_json())

    return jsonify(
        {
            "status": "OK",
            "tenants": tenantsList,
        }
    )


@app.route("/profile", methods=["GET"])  # type: ignore
@verify_session()
def get_user_profile():
    session_info = GetSessionInfoUseCase(SupertokensAuthRepository()).execute()
    profile_repo = InMemoryUserProfileRepository()

    try:
        profile_data = GetUserProfileUseCase(
            SupertokensAuthRepository(), profile_repo
        ).execute(session_info.user_id)
        return jsonify(profile_data)
    except UserNotFoundError:
        return jsonify({"error": "User not found"}), 404


@app.route("/profile", methods=["POST"])  # type: ignore
@verify_session()
def create_user_profile():
    session_info = GetSessionInfoUseCase(SupertokensAuthRepository()).execute()
    profile_repo = InMemoryUserProfileRepository()

    data = request.get_json()
    data["authentication_id"] = session_info.user_id

    try:
        profile = CreateUserProfileUseCase(profile_repo).execute(data)
        return jsonify({"status": "success", "profile_id": profile.id})
    except Exception as e:
        return jsonify({"error": str(e)}), 400


@app.route("/profile", methods=["PUT"])  # type: ignore
@verify_session()
def update_user_profile():
    session_info = GetSessionInfoUseCase(SupertokensAuthRepository()).execute()
    profile_repo = InMemoryUserProfileRepository()

    data = request.get_json()

    try:
        profile = UpdateUserProfileUseCase(profile_repo).execute(
            session_info.user_id, data
        )
        return jsonify({"status": "success", "profile_id": profile.id})
    except UserProfileError as e:
        return jsonify({"error": str(e)}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400


@app.route("/profile/check", methods=["GET"])  # type: ignore
@verify_session()
def check_profile_completion():
    session_info = GetSessionInfoUseCase(SupertokensAuthRepository()).execute()
    profile_repo = InMemoryUserProfileRepository()

    try:
        result = CheckProfileCompletionUseCase(profile_repo).execute(
            session_info.user_id
        )
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/profile/minimal", methods=["POST"])  # type: ignore
@verify_session()
def create_minimal_profile():
    session_info = GetSessionInfoUseCase(SupertokensAuthRepository()).execute()
    profile_repo = InMemoryUserProfileRepository()

    try:
        profile = CreateMinimalProfileUseCase(profile_repo).execute(
            session_info.user_id
        )
        return jsonify({"status": "success", "profile_id": profile.id})
    except Exception as e:
        return jsonify({"error": str(e)}), 400


# This is required since if this is not there, then OPTIONS requests for
# the APIs exposed by the supertokens' Middleware will return a 404
@app.route("/", defaults={"u_path": ""})  # type: ignore
@app.route("/<path:u_path>")  # type: ignore
def catch_all(u_path: str):  # pylint: disable=unused-argument
    abort(404)


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=int("3001"), debug=True)

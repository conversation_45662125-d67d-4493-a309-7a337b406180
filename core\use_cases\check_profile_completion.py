from typing import Dict, Any
from core.repositories.interfaces.user_profile_repository import IUserProfileRepository


class CheckProfileCompletionUseCase:
    def __init__(self, profile_repository: IUserProfileRepository):
        self._profile_repository = profile_repository
    
    def execute(self, user_id: str) -> Dict[str, Any]:
        profile = self._profile_repository.get_profile_by_user_id(user_id)
        
        if not profile:
            return {
                "exists": False,
                "is_complete": False,
                "is_minimal": False
            }
        
        # Check if profile is minimal (placeholder data)
        is_minimal = (
            profile.name.first_name == "User" and 
            profile.name.last_name.startswith("#")
        )
        
        # Check if profile has essential information
        is_complete = (
            not is_minimal and
            profile.name.first_name.strip() != "" and
            profile.name.last_name.strip() != ""
        )
        
        return {
            "exists": True,
            "is_complete": is_complete,
            "is_minimal": is_minimal,
            "profile": {
                "first_name": profile.name.first_name,
                "last_name": profile.name.last_name,
                "middle_name": profile.name.middle_name,
                "phone_number": profile.phone_number,
                "avatar_url": profile.avatar_url,
                "bio": profile.bio
            }
        }

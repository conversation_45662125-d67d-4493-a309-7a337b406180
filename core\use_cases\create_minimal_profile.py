from datetime import datetime
import uuid
from core.domain.user import <PERSON><PERSON><PERSON>, UserProfile
from core.repositories.interfaces.user_profile_repository import IUserProfileRepository


class CreateMinimalProfileUseCase:
    def __init__(self, profile_repository: IUserProfileRepository):
        self._profile_repository = profile_repository
    
    def execute(self, authentication_id: str) -> UserProfile:
        # Create minimal profile with placeholder data
        name = PersonName(
            first_name="User",
            last_name=f"#{authentication_id[:8]}",
            middle_name=None
        )
        
        profile = UserProfile(
            id=str(uuid.uuid4()),
            authentication_id=authentication_id,
            name=name,
            phone_number=None,
            avatar_url=None,
            bio=None,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        return self._profile_repository.create_profile(profile)

from datetime import datetime
from typing import Any, Dict
import uuid
from core.domain.user import <PERSON><PERSON><PERSON>, UserProfile
from core.repositories.interfaces.user_profile_repository import IUserProfileRepository


class CreateUserProfileUseCase:
    def __init__(self, profile_repository: IUserProfileRepository):
        self._profile_repository = profile_repository

    def execute(self, profile_data: Dict[str, Any]) -> UserProfile:
        # Create PersonName from form data
        name = PersonName(
            first_name=profile_data.get("first_name", ""),
            last_name=profile_data.get("last_name", ""),
            middle_name=profile_data.get("middle_name")
        )

        # Create UserProfile
        profile = UserProfile(
            id=str(uuid.uuid4()),
            authentication_id=profile_data["authentication_id"],
            name=name,
            phone_number=profile_data.get("phone_number"),
            avatar_url=profile_data.get("avatar_url"),
            bio=profile_data.get("bio"),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        return self._profile_repository.create_profile(profile)

from typing import Optional

from core.domain.authentication import SessionInfo
from core.repositories.interfaces.auth_repository import IAuthRepository


class GetSessionInfoUseCase:
    """Use case for getting session information."""

    def __init__(self, auth_repository: IAuthRepository):
        self._auth_repository = auth_repository

    def execute(self) -> Optional[SessionInfo]:
        """Execute the use case."""
        return self._auth_repository.get_current_session_info()

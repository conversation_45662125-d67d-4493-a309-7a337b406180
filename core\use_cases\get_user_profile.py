from typing import Dict, Optional

from core.domain.exceptions import UserNotFoundError
from core.domain.user import UserProfile
from core.repositories.interfaces.auth_repository import IAuthRepository
from core.repositories.interfaces.user_profile_repository import IUserProfileRepository


class GetUserProfileUseCase:
    def __init__(
        self,
        auth_repository: IAuthRepository,
        profile_repository: IUserProfileRepository,
    ):
        self._auth_repository = auth_repository
        self._profile_repository = profile_repository

    def execute(self, user_id: str) -> Dict[str, Optional[UserProfile]]:
        # Get SuperTokens user data
        supertokens_user = self._auth_repository.get_user_by_id(user_id)
        if not supertokens_user:
            raise UserNotFoundError(f"User {user_id} not found in SuperTokens")

        # Get business profile data
        user_profile = self._profile_repository.get_profile_by_user_id(user_id)

        return {
            "supertokens_data": {
                "user_id": supertokens_user.user_id,
                "email": supertokens_user.email,
                "phone_number": supertokens_user.phone_number,
                "time_joined": supertokens_user.time_joined,
            },
            "profile": {
                "name": user_profile.name.full_name if user_profile else None,
                "avatar_url": user_profile.avatar_url if user_profile else None,
                "bio": user_profile.bio if user_profile else None,
                "created_at": user_profile.created_at.isoformat()
                if user_profile and user_profile.created_at
                else None,
                "updated_at": user_profile.updated_at.isoformat()
                if user_profile and user_profile.updated_at
                else None,
            },
        }

from core.domain.authentication import TenantList
from core.repositories.interfaces.auth_repository import IAuthRepository


class ListTenantsUseCase:
    """Use case for listing tenants."""

    def __init__(self, auth_repository: IAuthRepository):
        self._auth_repository = auth_repository

    def execute(self) -> TenantList:
        """Execute the use case."""
        return self._auth_repository.get_all_tenants()

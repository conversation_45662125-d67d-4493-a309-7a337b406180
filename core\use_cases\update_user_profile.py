from datetime import datetime
from typing import Any, Dict
from core.domain.user import Person<PERSON><PERSON>, UserProfile
from core.repositories.interfaces.user_profile_repository import IUserProfileRepository
from core.domain.exceptions import UserProfileError


class UpdateUserProfileUseCase:
    def __init__(self, profile_repository: IUserProfileRepository):
        self._profile_repository = profile_repository
    
    def execute(self, user_id: str, profile_data: Dict[str, Any]) -> UserProfile:
        existing_profile = self._profile_repository.get_profile_by_user_id(user_id)
        if not existing_profile:
            raise UserProfileError(f"Profile for user {user_id} not found")
        
        # Create updated PersonName
        name = PersonName(
            first_name=profile_data.get("first_name", existing_profile.name.first_name),
            last_name=profile_data.get("last_name", existing_profile.name.last_name),
            middle_name=profile_data.get("middle_name", existing_profile.name.middle_name)
        )
        
        # Create updated UserProfile
        updated_profile = UserProfile(
            id=existing_profile.id,
            authentication_id=existing_profile.authentication_id,
            name=name,
            phone_number=profile_data.get("phone_number", existing_profile.phone_number),
            avatar_url=profile_data.get("avatar_url", existing_profile.avatar_url),
            bio=profile_data.get("bio", existing_profile.bio),
            created_at=existing_profile.created_at,
            updated_at=datetime.utcnow()
        )
        
        return self._profile_repository.update_profile(updated_profile)

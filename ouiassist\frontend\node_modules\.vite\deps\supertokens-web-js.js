import {
  require_build,
  require_cookieHandler,
  require_dateProvider,
  require_error,
  require_normalisedURLPath,
  require_recipe,
  require_recipeModule,
  require_utils,
  require_windowHandler
} from "./chunk-LZPXGRUM.js";
import {
  __commonJS
} from "./chunk-BUSYA2B4.js";

// node_modules/.pnpm/supertokens-website@20.1.6/node_modules/supertokens-website/utils/cookieHandler/index.js
var require_cookieHandler2 = __commonJS({
  "node_modules/.pnpm/supertokens-website@20.1.6/node_modules/supertokens-website/utils/cookieHandler/index.js"(exports) {
    "use strict";
    function __export(m) {
      for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];
    }
    exports.__esModule = true;
    __export(require_cookieHandler());
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/cookieHandler/index.js
var require_cookieHandler3 = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/cookieHandler/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.CookieHandlerReference = void 0;
    var cookieHandler_1 = require_cookieHandler2();
    Object.defineProperty(exports, "CookieHandlerReference", {
      enumerable: true,
      get: function() {
        return cookieHandler_1.CookieHandlerReference;
      }
    });
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/postSuperTokensInitCallbacks.js
var require_postSuperTokensInitCallbacks = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/postSuperTokensInitCallbacks.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.PostSuperTokensInitCallbacks = void 0;
    var PostSuperTokensInitCallbacks = (
      /** @class */
      function() {
        function PostSuperTokensInitCallbacks2() {
        }
        PostSuperTokensInitCallbacks2.addPostInitCallback = function(cb) {
          PostSuperTokensInitCallbacks2.postInitCallbacks.push(cb);
        };
        PostSuperTokensInitCallbacks2.runPostInitCallbacks = function() {
          for (var _i = 0, _a = PostSuperTokensInitCallbacks2.postInitCallbacks; _i < _a.length; _i++) {
            var cb = _a[_i];
            cb();
          }
        };
        PostSuperTokensInitCallbacks2.postInitCallbacks = [];
        return PostSuperTokensInitCallbacks2;
      }()
    );
    exports.PostSuperTokensInitCallbacks = PostSuperTokensInitCallbacks;
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/recipeModule/utils.js
var require_utils2 = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/recipeModule/utils.js"(exports) {
    "use strict";
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    var __generator = exports && exports.__generator || function(thisArg, body) {
      var _ = {
        label: 0,
        sent: function() {
          if (t[0] & 1) throw t[1];
          return t[1];
        },
        trys: [],
        ops: []
      }, f, y, t, g;
      return g = { next: verb(0), throw: verb(1), return: verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
      }), g;
      function verb(n) {
        return function(v) {
          return step([n, v]);
        };
      }
      function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_)
          try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done)
              return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
              case 0:
              case 1:
                t = op;
                break;
              case 4:
                _.label++;
                return { value: op[1], done: false };
              case 5:
                _.label++;
                y = op[1];
                op = [0];
                continue;
              case 7:
                op = _.ops.pop();
                _.trys.pop();
                continue;
              default:
                if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                  _ = 0;
                  continue;
                }
                if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                  _.label = op[1];
                  break;
                }
                if (op[0] === 6 && _.label < t[1]) {
                  _.label = t[1];
                  t = op;
                  break;
                }
                if (t && _.label < t[2]) {
                  _.label = t[2];
                  _.ops.push(op);
                  break;
                }
                if (t[2]) _.ops.pop();
                _.trys.pop();
                continue;
            }
            op = body.call(thisArg, _);
          } catch (e) {
            op = [6, e];
            y = 0;
          } finally {
            f = t = 0;
          }
        if (op[0] & 5) throw op[1];
        return { value: op[0] ? op[1] : void 0, done: true };
      }
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.normaliseRecipeModuleConfig = void 0;
    function normaliseRecipeModuleConfig(config) {
      var _this = this;
      var preAPIHook = config.preAPIHook;
      if (preAPIHook === void 0) {
        preAPIHook = function(context) {
          return __awaiter(_this, void 0, void 0, function() {
            return __generator(this, function(_a) {
              return [2, context];
            });
          });
        };
      }
      var postAPIHook = config.postAPIHook;
      if (postAPIHook === void 0) {
        postAPIHook = function() {
          return __awaiter(_this, void 0, void 0, function() {
            return __generator(this, function(_a) {
              return [
                2
                /*return*/
              ];
            });
          });
        };
      }
      return {
        recipeId: config.recipeId,
        appInfo: config.appInfo,
        clientType: config.clientType,
        preAPIHook,
        postAPIHook
      };
    }
    exports.normaliseRecipeModuleConfig = normaliseRecipeModuleConfig;
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/authRecipe/utils.js
var require_utils3 = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/authRecipe/utils.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.normaliseAuthRecipe = void 0;
    var utils_1 = require_utils2();
    function normaliseAuthRecipe(config) {
      return (0, utils_1.normaliseRecipeModuleConfig)(config);
    }
    exports.normaliseAuthRecipe = normaliseAuthRecipe;
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/multitenancy/utils.js
var require_utils4 = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/multitenancy/utils.js"(exports) {
    "use strict";
    var __assign = exports && exports.__assign || function() {
      __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
          s = arguments[i];
          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
      };
      return __assign.apply(this, arguments);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.normaliseUserInput = void 0;
    var utils_1 = require_utils3();
    function normaliseUserInput(config) {
      var override = __assign(
        {
          functions: function(originalImplementation) {
            return originalImplementation;
          }
        },
        config.override
      );
      return __assign(__assign({}, (0, utils_1.normaliseAuthRecipe)(config)), { override });
    }
    exports.normaliseUserInput = normaliseUserInput;
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/version.js
var require_version = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/version.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.supported_fdi = exports.package_version = void 0;
    exports.package_version = "0.15.0";
    exports.supported_fdi = ["3.1", "4.0", "4.1"];
  }
});

// node_modules/.pnpm/supertokens-website@20.1.6/node_modules/supertokens-website/utils/error/index.js
var require_error2 = __commonJS({
  "node_modules/.pnpm/supertokens-website@20.1.6/node_modules/supertokens-website/utils/error/index.js"(exports) {
    "use strict";
    function __export(m) {
      for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];
    }
    exports.__esModule = true;
    var d = require_error();
    if (d.default !== void 0) {
      __export(d);
    } else {
      __export({
        default: d,
        ...d
      });
    }
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/error.js
var require_error3 = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/error.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var error_1 = require_error2();
    exports.default = error_1.STGeneralError;
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/querier.js
var require_querier = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/querier.js"(exports) {
    "use strict";
    var __assign = exports && exports.__assign || function() {
      __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
          s = arguments[i];
          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
      };
      return __assign.apply(this, arguments);
    };
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    var __generator = exports && exports.__generator || function(thisArg, body) {
      var _ = {
        label: 0,
        sent: function() {
          if (t[0] & 1) throw t[1];
          return t[1];
        },
        trys: [],
        ops: []
      }, f, y, t, g;
      return g = { next: verb(0), throw: verb(1), return: verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
      }), g;
      function verb(n) {
        return function(v) {
          return step([n, v]);
        };
      }
      function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_)
          try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done)
              return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
              case 0:
              case 1:
                t = op;
                break;
              case 4:
                _.label++;
                return { value: op[1], done: false };
              case 5:
                _.label++;
                y = op[1];
                op = [0];
                continue;
              case 7:
                op = _.ops.pop();
                _.trys.pop();
                continue;
              default:
                if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                  _ = 0;
                  continue;
                }
                if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                  _.label = op[1];
                  break;
                }
                if (op[0] === 6 && _.label < t[1]) {
                  _.label = t[1];
                  t = op;
                  break;
                }
                if (t && _.label < t[2]) {
                  _.label = t[2];
                  _.ops.push(op);
                  break;
                }
                if (t[2]) _.ops.pop();
                _.trys.pop();
                continue;
            }
            op = body.call(thisArg, _);
          } catch (e) {
            op = [6, e];
            y = 0;
          } finally {
            f = t = 0;
          }
        if (op[0] & 5) throw op[1];
        return { value: op[0] ? op[1] : void 0, done: true };
      }
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    var normalisedURLPath_1 = require_normalisedURLPath();
    var version_1 = require_version();
    var error_1 = require_error3();
    var Querier = (
      /** @class */
      function() {
        function Querier2(recipeId, appInfo) {
          var _this = this;
          this.recipeId = recipeId;
          this.appInfo = appInfo;
          this.get = function(tenantId, path, config, queryParams, preAPIHook, postAPIHook) {
            return __awaiter(_this, void 0, void 0, function() {
              var result, jsonBody;
              return __generator(this, function(_b) {
                switch (_b.label) {
                  case 0:
                    return [
                      4,
                      this.fetch(
                        this.getFullUrl(tenantId, path, queryParams),
                        __assign({ method: "GET" }, config),
                        preAPIHook,
                        postAPIHook
                      )
                    ];
                  case 1:
                    result = _b.sent();
                    return [4, this.getResponseJsonOrThrowGeneralError(result)];
                  case 2:
                    jsonBody = _b.sent();
                    return [
                      2,
                      {
                        jsonBody,
                        fetchResponse: result
                      }
                    ];
                }
              });
            });
          };
          this.post = function(tenantId, path, config, preAPIHook, postAPIHook) {
            return __awaiter(_this, void 0, void 0, function() {
              var result, jsonBody;
              return __generator(this, function(_b) {
                switch (_b.label) {
                  case 0:
                    if (config.body === void 0) {
                      throw new Error("Post request must have a body");
                    }
                    return [
                      4,
                      this.fetch(
                        this.getFullUrl(tenantId, path),
                        __assign({ method: "POST" }, config),
                        preAPIHook,
                        postAPIHook
                      )
                    ];
                  case 1:
                    result = _b.sent();
                    return [4, this.getResponseJsonOrThrowGeneralError(result)];
                  case 2:
                    jsonBody = _b.sent();
                    return [
                      2,
                      {
                        jsonBody,
                        fetchResponse: result
                      }
                    ];
                }
              });
            });
          };
          this.delete = function(tenantId, path, config, preAPIHook, postAPIHook) {
            return __awaiter(_this, void 0, void 0, function() {
              var result, jsonBody;
              return __generator(this, function(_b) {
                switch (_b.label) {
                  case 0:
                    return [
                      4,
                      this.fetch(
                        this.getFullUrl(tenantId, path),
                        __assign({ method: "DELETE" }, config),
                        preAPIHook,
                        postAPIHook
                      )
                    ];
                  case 1:
                    result = _b.sent();
                    return [4, this.getResponseJsonOrThrowGeneralError(result)];
                  case 2:
                    jsonBody = _b.sent();
                    return [
                      2,
                      {
                        jsonBody,
                        fetchResponse: result
                      }
                    ];
                }
              });
            });
          };
          this.put = function(tenantId, path, config, preAPIHook, postAPIHook) {
            return __awaiter(_this, void 0, void 0, function() {
              var result, jsonBody;
              return __generator(this, function(_b) {
                switch (_b.label) {
                  case 0:
                    return [
                      4,
                      this.fetch(
                        this.getFullUrl(tenantId, path),
                        __assign({ method: "PUT" }, config),
                        preAPIHook,
                        postAPIHook
                      )
                    ];
                  case 1:
                    result = _b.sent();
                    return [4, this.getResponseJsonOrThrowGeneralError(result)];
                  case 2:
                    jsonBody = _b.sent();
                    return [
                      2,
                      {
                        jsonBody,
                        fetchResponse: result
                      }
                    ];
                }
              });
            });
          };
          this.fetch = function(url, config, preAPIHook, postAPIHook) {
            return __awaiter(_this, void 0, void 0, function() {
              var headers, _b, requestInit, modifiedUrl, result, reponseForPostAPI;
              return __generator(this, function(_c) {
                switch (_c.label) {
                  case 0:
                    if (config === void 0) {
                      headers = {};
                    } else {
                      headers = config.headers;
                    }
                    return [
                      4,
                      this.callPreAPIHook({
                        preAPIHook,
                        url,
                        requestInit: __assign(__assign({}, config), {
                          headers: __assign(__assign({}, headers), {
                            "fdi-version": version_1.supported_fdi.join(","),
                            "Content-Type": "application/json",
                            rid: this.recipeId
                          })
                        })
                      })
                    ];
                  case 1:
                    _b = _c.sent(), requestInit = _b.requestInit, modifiedUrl = _b.url;
                    return [4, fetch(modifiedUrl, requestInit)];
                  case 2:
                    result = _c.sent();
                    if (result.status >= 300) {
                      throw result;
                    }
                    if (!(postAPIHook !== void 0)) return [3, 4];
                    reponseForPostAPI = result.clone();
                    return [
                      4,
                      postAPIHook({
                        requestInit,
                        url,
                        fetchResponse: reponseForPostAPI
                      })
                    ];
                  case 3:
                    _c.sent();
                    _c.label = 4;
                  case 4:
                    return [2, result];
                }
              });
            });
          };
          this.callPreAPIHook = function(context) {
            return __awaiter(_this, void 0, void 0, function() {
              var result;
              return __generator(this, function(_b) {
                switch (_b.label) {
                  case 0:
                    if (context.preAPIHook === void 0) {
                      return [
                        2,
                        {
                          url: context.url,
                          requestInit: context.requestInit
                        }
                      ];
                    }
                    return [
                      4,
                      context.preAPIHook({
                        url: context.url,
                        requestInit: context.requestInit
                      })
                    ];
                  case 1:
                    result = _b.sent();
                    return [2, result];
                }
              });
            });
          };
          this.getFullUrl = function(tenantId, pathStr, queryParams) {
            var basePath = _this.appInfo.apiBasePath.getAsStringDangerous();
            if (tenantId !== void 0 && tenantId !== "public") {
              basePath = "".concat(basePath, "/").concat(tenantId);
            }
            var path = new normalisedURLPath_1.default(pathStr);
            var fullUrl = "".concat(_this.appInfo.apiDomain.getAsStringDangerous()).concat(basePath).concat(path.getAsStringDangerous());
            if (queryParams === void 0) {
              return fullUrl;
            }
            return fullUrl + "?" + new URLSearchParams(queryParams);
          };
          this.getResponseJsonOrThrowGeneralError = function(response) {
            return __awaiter(_this, void 0, void 0, function() {
              var json, message;
              return __generator(this, function(_b) {
                switch (_b.label) {
                  case 0:
                    return [4, response.clone().json()];
                  case 1:
                    json = _b.sent();
                    if (json.status === "GENERAL_ERROR") {
                      message = json.message === void 0 ? "No Error Message Provided" : json.message;
                      throw new error_1.default(message);
                    }
                    return [2, json];
                }
              });
            });
          };
        }
        var _a;
        _a = Querier2;
        Querier2.preparePreAPIHook = function(_b) {
          var recipePreAPIHook = _b.recipePreAPIHook, action = _b.action, options = _b.options, userContext = _b.userContext;
          return function(context) {
            return __awaiter(void 0, void 0, void 0, function() {
              var postRecipeHookContext;
              return __generator(_a, function(_b2) {
                switch (_b2.label) {
                  case 0:
                    return [
                      4,
                      recipePreAPIHook(
                        __assign(__assign({}, context), { action, userContext })
                      )
                    ];
                  case 1:
                    postRecipeHookContext = _b2.sent();
                    if (options === void 0 || options.preAPIHook === void 0) {
                      return [2, postRecipeHookContext];
                    }
                    return [
                      2,
                      options.preAPIHook({
                        url: postRecipeHookContext.url,
                        requestInit: postRecipeHookContext.requestInit,
                        userContext
                      })
                    ];
                }
              });
            });
          };
        };
        Querier2.preparePostAPIHook = function(_b) {
          var recipePostAPIHook = _b.recipePostAPIHook, action = _b.action, userContext = _b.userContext;
          return function(context) {
            return __awaiter(void 0, void 0, void 0, function() {
              return __generator(_a, function(_b2) {
                switch (_b2.label) {
                  case 0:
                    return [
                      4,
                      recipePostAPIHook(
                        __assign(__assign({}, context), { userContext, action })
                      )
                    ];
                  case 1:
                    _b2.sent();
                    return [
                      2
                      /*return*/
                    ];
                }
              });
            });
          };
        };
        return Querier2;
      }()
    );
    exports.default = Querier;
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/multitenancy/recipeImplementation.js
var require_recipeImplementation = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/multitenancy/recipeImplementation.js"(exports) {
    "use strict";
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    var __generator = exports && exports.__generator || function(thisArg, body) {
      var _ = {
        label: 0,
        sent: function() {
          if (t[0] & 1) throw t[1];
          return t[1];
        },
        trys: [],
        ops: []
      }, f, y, t, g;
      return g = { next: verb(0), throw: verb(1), return: verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
      }), g;
      function verb(n) {
        return function(v) {
          return step([n, v]);
        };
      }
      function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_)
          try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done)
              return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
              case 0:
              case 1:
                t = op;
                break;
              case 4:
                _.label++;
                return { value: op[1], done: false };
              case 5:
                _.label++;
                y = op[1];
                op = [0];
                continue;
              case 7:
                op = _.ops.pop();
                _.trys.pop();
                continue;
              default:
                if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                  _ = 0;
                  continue;
                }
                if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                  _.label = op[1];
                  break;
                }
                if (op[0] === 6 && _.label < t[1]) {
                  _.label = t[1];
                  t = op;
                  break;
                }
                if (t && _.label < t[2]) {
                  _.label = t[2];
                  _.ops.push(op);
                  break;
                }
                if (t[2]) _.ops.pop();
                _.trys.pop();
                continue;
            }
            op = body.call(thisArg, _);
          } catch (e) {
            op = [6, e];
            y = 0;
          } finally {
            f = t = 0;
          }
        if (op[0] & 5) throw op[1];
        return { value: op[0] ? op[1] : void 0, done: true };
      }
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.getRecipeImplementation = void 0;
    var querier_1 = require_querier();
    var utils_1 = require_utils();
    function getRecipeImplementation(recipeImplInput) {
      var querier = new querier_1.default(recipeImplInput.recipeId, recipeImplInput.appInfo);
      return {
        getTenantId: function() {
          var queryParam = (0, utils_1.getQueryParams)("tenantId");
          if ((queryParam === null || queryParam === void 0 ? void 0 : queryParam.trim()) === "") {
            return void 0;
          }
          return queryParam;
        },
        getLoginMethods: function(_a) {
          var tenantId = _a.tenantId, options = _a.options, userContext = _a.userContext;
          return __awaiter(this, void 0, void 0, function() {
            var queryParams, _b, jsonBody, fetchResponse, firstFactors;
            return __generator(this, function(_c) {
              switch (_c.label) {
                case 0:
                  queryParams = {};
                  if (recipeImplInput.clientType !== void 0) {
                    queryParams.clientType = recipeImplInput.clientType;
                  }
                  return [
                    4,
                    querier.get(
                      tenantId,
                      "/loginmethods",
                      {},
                      queryParams,
                      querier_1.default.preparePreAPIHook({
                        recipePreAPIHook: recipeImplInput.preAPIHook,
                        action: "GET_LOGIN_METHODS",
                        options,
                        userContext
                      }),
                      querier_1.default.preparePostAPIHook({
                        recipePostAPIHook: recipeImplInput.postAPIHook,
                        action: "GET_LOGIN_METHODS",
                        userContext
                      })
                    )
                  ];
                case 1:
                  _b = _c.sent(), jsonBody = _b.jsonBody, fetchResponse = _b.fetchResponse;
                  if (jsonBody.firstFactors === void 0) {
                    firstFactors = [];
                    if (jsonBody.emailPassword.enabled) {
                      firstFactors.push("emailpassword");
                    }
                    if (jsonBody.thirdParty.enabled) {
                      firstFactors.push("thirdparty");
                    }
                    if (jsonBody.passwordless.enabled) {
                      firstFactors.push("otp-email");
                      firstFactors.push("otp-phone");
                      firstFactors.push("link-email");
                      firstFactors.push("link-phone");
                    }
                  } else {
                    firstFactors = jsonBody.firstFactors;
                  }
                  return [
                    2,
                    {
                      status: "OK",
                      thirdParty: {
                        providers: jsonBody.thirdParty.providers
                      },
                      firstFactors,
                      fetchResponse
                    }
                  ];
              }
            });
          });
        }
      };
    }
    exports.default = getRecipeImplementation;
    exports.getRecipeImplementation = getRecipeImplementation;
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/authRecipe/index.js
var require_authRecipe = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/authRecipe/index.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    var __generator = exports && exports.__generator || function(thisArg, body) {
      var _ = {
        label: 0,
        sent: function() {
          if (t[0] & 1) throw t[1];
          return t[1];
        },
        trys: [],
        ops: []
      }, f, y, t, g;
      return g = { next: verb(0), throw: verb(1), return: verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
      }), g;
      function verb(n) {
        return function(v) {
          return step([n, v]);
        };
      }
      function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_)
          try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done)
              return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
              case 0:
              case 1:
                t = op;
                break;
              case 4:
                _.label++;
                return { value: op[1], done: false };
              case 5:
                _.label++;
                y = op[1];
                op = [0];
                continue;
              case 7:
                op = _.ops.pop();
                _.trys.pop();
                continue;
              default:
                if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                  _ = 0;
                  continue;
                }
                if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                  _.label = op[1];
                  break;
                }
                if (op[0] === 6 && _.label < t[1]) {
                  _.label = t[1];
                  t = op;
                  break;
                }
                if (t && _.label < t[2]) {
                  _.label = t[2];
                  _.ops.push(op);
                  break;
                }
                if (t[2]) _.ops.pop();
                _.trys.pop();
                continue;
            }
            op = body.call(thisArg, _);
          } catch (e) {
            op = [6, e];
            y = 0;
          } finally {
            f = t = 0;
          }
        if (op[0] & 5) throw op[1];
        return { value: op[0] ? op[1] : void 0, done: true };
      }
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    var recipeModule_1 = require_recipeModule();
    var recipe_1 = require_recipe();
    var AuthRecipe = (
      /** @class */
      function(_super) {
        __extends(AuthRecipe2, _super);
        function AuthRecipe2(config) {
          var _this = _super.call(this, config) || this;
          _this.signOut = function(input) {
            return __awaiter(_this, void 0, void 0, function() {
              return __generator(this, function(_a) {
                switch (_a.label) {
                  case 0:
                    return [
                      4,
                      recipe_1.default.getInstanceOrThrow().signOut({
                        userContext: input.userContext
                      })
                    ];
                  case 1:
                    return [2, _a.sent()];
                }
              });
            });
          };
          return _this;
        }
        return AuthRecipe2;
      }(recipeModule_1.default)
    );
    exports.default = AuthRecipe;
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/multitenancy/recipe.js
var require_recipe2 = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/multitenancy/recipe.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    var __assign = exports && exports.__assign || function() {
      __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
          s = arguments[i];
          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
      };
      return __assign.apply(this, arguments);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Recipe = void 0;
    var utils_1 = require_utils4();
    var supertokens_js_override_1 = require_build();
    var recipeImplementation_1 = require_recipeImplementation();
    var utils_2 = require_utils();
    var authRecipe_1 = require_authRecipe();
    var Recipe = (
      /** @class */
      function(_super) {
        __extends(Recipe2, _super);
        function Recipe2(config) {
          var _this = _super.call(this, (0, utils_1.normaliseUserInput)(config)) || this;
          var builder = new supertokens_js_override_1.default(
            (0, recipeImplementation_1.default)({
              recipeId: _this.config.recipeId,
              appInfo: _this.config.appInfo,
              clientType: _this.config.clientType,
              preAPIHook: _this.config.preAPIHook,
              postAPIHook: _this.config.postAPIHook
            })
          );
          _this.recipeImplementation = builder.override(_this.config.override.functions).build();
          return _this;
        }
        Recipe2.init = function(config) {
          return function(appInfo, clientType) {
            Recipe2.instance = new Recipe2(
              __assign(__assign({}, config), { recipeId: Recipe2.RECIPE_ID, appInfo, clientType })
            );
            return Recipe2.instance;
          };
        };
        Recipe2.getInstanceOrThrow = function() {
          if (Recipe2.instance === void 0) {
            var error = "No instance of Multitenancy found. Ensure that 'SuperTokens.init' method has been called.";
            error = (0, utils_2.checkForSSRErrorAndAppendIfNeeded)(error);
            throw Error(error);
          }
          return Recipe2.instance;
        };
        Recipe2.reset = function() {
          if (!(0, utils_2.isTest)()) {
            return;
          }
          Recipe2.instance = void 0;
          return;
        };
        Recipe2.RECIPE_ID = "multitenancy";
        return Recipe2;
      }(authRecipe_1.default)
    );
    exports.Recipe = Recipe;
    exports.default = Recipe;
  }
});

// node_modules/.pnpm/supertokens-website@20.1.6/node_modules/supertokens-website/utils/dateProvider/index.js
var require_dateProvider2 = __commonJS({
  "node_modules/.pnpm/supertokens-website@20.1.6/node_modules/supertokens-website/utils/dateProvider/index.js"(exports) {
    "use strict";
    function __export(m) {
      for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];
    }
    exports.__esModule = true;
    __export(require_dateProvider());
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/dateProvider/index.js
var require_dateProvider3 = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/dateProvider/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.DateProviderReference = void 0;
    var dateProvider_1 = require_dateProvider2();
    Object.defineProperty(exports, "DateProviderReference", {
      enumerable: true,
      get: function() {
        return dateProvider_1.DateProviderReference;
      }
    });
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/supertokens.js
var require_supertokens = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/supertokens.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var utils_1 = require_utils();
    var cookieHandler_1 = require_cookieHandler3();
    var windowHandler_1 = require_windowHandler();
    var postSuperTokensInitCallbacks_1 = require_postSuperTokensInitCallbacks();
    var recipe_1 = require_recipe2();
    var dateProvider_1 = require_dateProvider3();
    var SuperTokens = (
      /** @class */
      function() {
        function SuperTokens2(config) {
          var _this = this;
          this.recipeList = [];
          this.appInfo = (0, utils_1.normaliseInputAppInfoOrThrowError)(config.appInfo);
          if (config.recipeList === void 0 || config.recipeList.length === 0) {
            throw new Error(
              "Please provide at least one recipe to the supertokens.init function call. See https://supertokens.io/docs/emailpassword/quick-setup/frontend"
            );
          }
          var enableDebugLogs = false;
          if (config.enableDebugLogs !== void 0) {
            enableDebugLogs = config.enableDebugLogs;
          }
          var multitenancyFound = false;
          this.recipeList = config.recipeList.map(function(recipe) {
            var recipeInstance = recipe(_this.appInfo, config.clientType, enableDebugLogs);
            if (recipeInstance.config.recipeId === recipe_1.Recipe.RECIPE_ID) {
              multitenancyFound = true;
            }
            return recipeInstance;
          });
          if (!multitenancyFound) {
            this.recipeList.push(recipe_1.Recipe.init()(this.appInfo, config.clientType, enableDebugLogs));
          }
        }
        SuperTokens2.init = function(config) {
          cookieHandler_1.CookieHandlerReference.init(config.cookieHandler);
          windowHandler_1.WindowHandlerReference.init(config.windowHandler);
          dateProvider_1.DateProviderReference.init(config.dateProvider);
          if (SuperTokens2.instance !== void 0) {
            console.warn("SuperTokens was already initialized");
            return;
          }
          SuperTokens2.instance = new SuperTokens2(config);
          postSuperTokensInitCallbacks_1.PostSuperTokensInitCallbacks.runPostInitCallbacks();
        };
        SuperTokens2.getInstanceOrThrow = function() {
          if (SuperTokens2.instance === void 0) {
            var error = "SuperTokens must be initialized before calling this method.";
            error = (0, utils_1.checkForSSRErrorAndAppendIfNeeded)(error);
            throw new Error(error);
          }
          return SuperTokens2.instance;
        };
        SuperTokens2.reset = function() {
          if (!(0, utils_1.isTest)()) {
            console.warn("Calling reset() is only supported during testing");
            return;
          }
          recipe_1.Recipe.reset();
          SuperTokens2.instance = void 0;
          return;
        };
        return SuperTokens2;
      }()
    );
    exports.default = SuperTokens;
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/index.js
var require_build2 = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.init = void 0;
    var supertokens_1 = require_supertokens();
    var SuperTokensAPIWrapper = (
      /** @class */
      function() {
        function SuperTokensAPIWrapper2() {
        }
        SuperTokensAPIWrapper2.init = function(config) {
          supertokens_1.default.init(config);
        };
        return SuperTokensAPIWrapper2;
      }()
    );
    exports.default = SuperTokensAPIWrapper;
    exports.init = SuperTokensAPIWrapper.init;
  }
});

// node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/index.js
var require_supertokens_web_js = __commonJS({
  "node_modules/.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/index.js"(exports) {
    function __export(m) {
      for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];
    }
    exports.__esModule = true;
    __export(require_build2());
  }
});
export default require_supertokens_web_js();
//# sourceMappingURL=supertokens-web-js.js.map

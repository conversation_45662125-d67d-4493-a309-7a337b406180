{"version": 3, "sources": ["../../.pnpm/supertokens-website@20.1.6/node_modules/supertokens-website/utils/cookieHandler/index.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/cookieHandler/index.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/postSuperTokensInitCallbacks.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/recipeModule/utils.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/authRecipe/utils.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/multitenancy/utils.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/version.js", "../../.pnpm/supertokens-website@20.1.6/node_modules/supertokens-website/utils/error/index.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/error.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/querier.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/multitenancy/recipeImplementation.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/authRecipe/index.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/multitenancy/recipe.js", "../../.pnpm/supertokens-website@20.1.6/node_modules/supertokens-website/utils/dateProvider/index.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/dateProvider/index.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/supertokens.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/index.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/index.js"], "sourcesContent": ["/* Copyright (c) 2020, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n * \n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n * \n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License. \n*/\n\"use strict\";\nfunction __export(m) {\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\n}\nexports.__esModule = true;\n__export(require(\"../../lib/build/utils/cookieHandler\"));\n", "\"use strict\";\n/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CookieHandlerReference = void 0;\nvar cookieHandler_1 = require(\"supertokens-website/utils/cookieHandler\");\nObject.defineProperty(exports, \"CookieHandlerReference\", {\n    enumerable: true,\n    get: function () {\n        return cookieHandler_1.CookieHandlerReference;\n    },\n});\n", "\"use strict\";\n/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.PostSuperTokensInitCallbacks = void 0;\nvar PostSuperTokensInitCallbacks = /** @class */ (function () {\n    function PostSuperTokensInitCallbacks() {}\n    PostSuperTokensInitCallbacks.addPostInitCallback = function (cb) {\n        PostSuperTokensInitCallbacks.postInitCallbacks.push(cb);\n    };\n    PostSuperTokensInitCallbacks.runPostInitCallbacks = function () {\n        for (var _i = 0, _a = PostSuperTokensInitCallbacks.postInitCallbacks; _i < _a.length; _i++) {\n            var cb = _a[_i];\n            cb();\n        }\n    };\n    PostSuperTokensInitCallbacks.postInitCallbacks = [];\n    return PostSuperTokensInitCallbacks;\n})();\nexports.PostSuperTokensInitCallbacks = PostSuperTokensInitCallbacks;\n", "\"use strict\";\nvar __awaiter =\n    (this && this.__awaiter) ||\n    function (thisArg, _arguments, P, generator) {\n        function adopt(value) {\n            return value instanceof P\n                ? value\n                : new P(function (resolve) {\n                      resolve(value);\n                  });\n        }\n        return new (P || (P = Promise))(function (resolve, reject) {\n            function fulfilled(value) {\n                try {\n                    step(generator.next(value));\n                } catch (e) {\n                    reject(e);\n                }\n            }\n            function rejected(value) {\n                try {\n                    step(generator[\"throw\"](value));\n                } catch (e) {\n                    reject(e);\n                }\n            }\n            function step(result) {\n                result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n            }\n            step((generator = generator.apply(thisArg, _arguments || [])).next());\n        });\n    };\nvar __generator =\n    (this && this.__generator) ||\n    function (thisArg, body) {\n        var _ = {\n                label: 0,\n                sent: function () {\n                    if (t[0] & 1) throw t[1];\n                    return t[1];\n                },\n                trys: [],\n                ops: [],\n            },\n            f,\n            y,\n            t,\n            g;\n        return (\n            (g = { next: verb(0), throw: verb(1), return: verb(2) }),\n            typeof Symbol === \"function\" &&\n                (g[Symbol.iterator] = function () {\n                    return this;\n                }),\n            g\n        );\n        function verb(n) {\n            return function (v) {\n                return step([n, v]);\n            };\n        }\n        function step(op) {\n            if (f) throw new TypeError(\"Generator is already executing.\");\n            while (_)\n                try {\n                    if (\n                        ((f = 1),\n                        y &&\n                            (t =\n                                op[0] & 2\n                                    ? y[\"return\"]\n                                    : op[0]\n                                    ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0)\n                                    : y.next) &&\n                            !(t = t.call(y, op[1])).done)\n                    )\n                        return t;\n                    if (((y = 0), t)) op = [op[0] & 2, t.value];\n                    switch (op[0]) {\n                        case 0:\n                        case 1:\n                            t = op;\n                            break;\n                        case 4:\n                            _.label++;\n                            return { value: op[1], done: false };\n                        case 5:\n                            _.label++;\n                            y = op[1];\n                            op = [0];\n                            continue;\n                        case 7:\n                            op = _.ops.pop();\n                            _.trys.pop();\n                            continue;\n                        default:\n                            if (\n                                !((t = _.trys), (t = t.length > 0 && t[t.length - 1])) &&\n                                (op[0] === 6 || op[0] === 2)\n                            ) {\n                                _ = 0;\n                                continue;\n                            }\n                            if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) {\n                                _.label = op[1];\n                                break;\n                            }\n                            if (op[0] === 6 && _.label < t[1]) {\n                                _.label = t[1];\n                                t = op;\n                                break;\n                            }\n                            if (t && _.label < t[2]) {\n                                _.label = t[2];\n                                _.ops.push(op);\n                                break;\n                            }\n                            if (t[2]) _.ops.pop();\n                            _.trys.pop();\n                            continue;\n                    }\n                    op = body.call(thisArg, _);\n                } catch (e) {\n                    op = [6, e];\n                    y = 0;\n                } finally {\n                    f = t = 0;\n                }\n            if (op[0] & 5) throw op[1];\n            return { value: op[0] ? op[1] : void 0, done: true };\n        }\n    };\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.normaliseRecipeModuleConfig = void 0;\nfunction normaliseRecipeModuleConfig(config) {\n    var _this = this;\n    var preAPIHook = config.preAPIHook;\n    if (preAPIHook === undefined) {\n        preAPIHook = function (context) {\n            return __awaiter(_this, void 0, void 0, function () {\n                return __generator(this, function (_a) {\n                    return [2 /*return*/, context];\n                });\n            });\n        };\n    }\n    var postAPIHook = config.postAPIHook;\n    if (postAPIHook === undefined) {\n        postAPIHook = function () {\n            return __awaiter(_this, void 0, void 0, function () {\n                return __generator(this, function (_a) {\n                    return [2 /*return*/];\n                });\n            });\n        };\n    }\n    return {\n        recipeId: config.recipeId,\n        appInfo: config.appInfo,\n        clientType: config.clientType,\n        preAPIHook: preAPIHook,\n        postAPIHook: postAPIHook,\n    };\n}\nexports.normaliseRecipeModuleConfig = normaliseRecipeModuleConfig;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.normaliseAuthRecipe = void 0;\n/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\nvar utils_1 = require(\"../recipeModule/utils\");\nfunction normaliseAuthRecipe(config) {\n    return (0, utils_1.normaliseRecipeModuleConfig)(config);\n}\nexports.normaliseAuthRecipe = normaliseAuthRecipe;\n", "\"use strict\";\nvar __assign =\n    (this && this.__assign) ||\n    function () {\n        __assign =\n            Object.assign ||\n            function (t) {\n                for (var s, i = 1, n = arguments.length; i < n; i++) {\n                    s = arguments[i];\n                    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n                }\n                return t;\n            };\n        return __assign.apply(this, arguments);\n    };\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.normaliseUserInput = void 0;\n/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\nvar utils_1 = require(\"../authRecipe/utils\");\nfunction normaliseUserInput(config) {\n    var override = __assign(\n        {\n            functions: function (originalImplementation) {\n                return originalImplementation;\n            },\n        },\n        config.override\n    );\n    return __assign(__assign({}, (0, utils_1.normaliseAuthRecipe)(config)), { override: override });\n}\nexports.normaliseUserInput = normaliseUserInput;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.supported_fdi = exports.package_version = void 0;\n/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\nexports.package_version = \"0.15.0\";\nexports.supported_fdi = [\"3.1\", \"4.0\", \"4.1\"];\n", "/* Copyright (c) 2020, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\n\"use strict\";\nfunction __export(m) {\n  for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\n}\nexports.__esModule = true;\n\nlet d = require(\"../../lib/build/error\");\n\nif (d.default !== undefined) {\n    __export(d);\n} else {\n    __export({\n        default: d,\n        ...d,\n    });\n}", "\"use strict\";\n/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/**\n * This error usually indicates that the API exposed by the backend SDKs responded\n * with `{status: \"GENERAL_ERROR\"}`. This should be used to show errors to the user\n * in your frontend application.\n */\nvar error_1 = require(\"supertokens-website/utils/error\");\nexports.default = error_1.STGeneralError;\n", "\"use strict\";\nvar __assign =\n    (this && this.__assign) ||\n    function () {\n        __assign =\n            Object.assign ||\n            function (t) {\n                for (var s, i = 1, n = arguments.length; i < n; i++) {\n                    s = arguments[i];\n                    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n                }\n                return t;\n            };\n        return __assign.apply(this, arguments);\n    };\nvar __awaiter =\n    (this && this.__awaiter) ||\n    function (thisArg, _arguments, P, generator) {\n        function adopt(value) {\n            return value instanceof P\n                ? value\n                : new P(function (resolve) {\n                      resolve(value);\n                  });\n        }\n        return new (P || (P = Promise))(function (resolve, reject) {\n            function fulfilled(value) {\n                try {\n                    step(generator.next(value));\n                } catch (e) {\n                    reject(e);\n                }\n            }\n            function rejected(value) {\n                try {\n                    step(generator[\"throw\"](value));\n                } catch (e) {\n                    reject(e);\n                }\n            }\n            function step(result) {\n                result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n            }\n            step((generator = generator.apply(thisArg, _arguments || [])).next());\n        });\n    };\nvar __generator =\n    (this && this.__generator) ||\n    function (thisArg, body) {\n        var _ = {\n                label: 0,\n                sent: function () {\n                    if (t[0] & 1) throw t[1];\n                    return t[1];\n                },\n                trys: [],\n                ops: [],\n            },\n            f,\n            y,\n            t,\n            g;\n        return (\n            (g = { next: verb(0), throw: verb(1), return: verb(2) }),\n            typeof Symbol === \"function\" &&\n                (g[Symbol.iterator] = function () {\n                    return this;\n                }),\n            g\n        );\n        function verb(n) {\n            return function (v) {\n                return step([n, v]);\n            };\n        }\n        function step(op) {\n            if (f) throw new TypeError(\"Generator is already executing.\");\n            while (_)\n                try {\n                    if (\n                        ((f = 1),\n                        y &&\n                            (t =\n                                op[0] & 2\n                                    ? y[\"return\"]\n                                    : op[0]\n                                    ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0)\n                                    : y.next) &&\n                            !(t = t.call(y, op[1])).done)\n                    )\n                        return t;\n                    if (((y = 0), t)) op = [op[0] & 2, t.value];\n                    switch (op[0]) {\n                        case 0:\n                        case 1:\n                            t = op;\n                            break;\n                        case 4:\n                            _.label++;\n                            return { value: op[1], done: false };\n                        case 5:\n                            _.label++;\n                            y = op[1];\n                            op = [0];\n                            continue;\n                        case 7:\n                            op = _.ops.pop();\n                            _.trys.pop();\n                            continue;\n                        default:\n                            if (\n                                !((t = _.trys), (t = t.length > 0 && t[t.length - 1])) &&\n                                (op[0] === 6 || op[0] === 2)\n                            ) {\n                                _ = 0;\n                                continue;\n                            }\n                            if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) {\n                                _.label = op[1];\n                                break;\n                            }\n                            if (op[0] === 6 && _.label < t[1]) {\n                                _.label = t[1];\n                                t = op;\n                                break;\n                            }\n                            if (t && _.label < t[2]) {\n                                _.label = t[2];\n                                _.ops.push(op);\n                                break;\n                            }\n                            if (t[2]) _.ops.pop();\n                            _.trys.pop();\n                            continue;\n                    }\n                    op = body.call(thisArg, _);\n                } catch (e) {\n                    op = [6, e];\n                    y = 0;\n                } finally {\n                    f = t = 0;\n                }\n            if (op[0] & 5) throw op[1];\n            return { value: op[0] ? op[1] : void 0, done: true };\n        }\n    };\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\nvar normalisedURLPath_1 = require(\"./normalisedURLPath\");\nvar version_1 = require(\"./version\");\nvar error_1 = require(\"./error\");\n/**\n * When network calls are made the Querier calls .clone() on the response before:\n * 1. Calling the post API hook\n * 2. Calling .json() when trying to read the body\n *\n * This is because the SDK needs to read the json body but we also want to allow users to read\n * the json body themselves (either in the post api hook or from the result of recipe functions)\n * for custom response handling. Since the body can only be read once we use .clone() to allow\n * for multiple reads.\n */\nvar Querier = /** @class */ (function () {\n    function Querier(recipeId, appInfo) {\n        var _this = this;\n        this.recipeId = recipeId;\n        this.appInfo = appInfo;\n        this.get = function (tenantId, path, config, queryParams, preAPIHook, postAPIHook) {\n            return __awaiter(_this, void 0, void 0, function () {\n                var result, jsonBody;\n                return __generator(this, function (_b) {\n                    switch (_b.label) {\n                        case 0:\n                            return [\n                                4 /*yield*/,\n                                this.fetch(\n                                    this.getFullUrl(tenantId, path, queryParams),\n                                    __assign({ method: \"GET\" }, config),\n                                    preAPIHook,\n                                    postAPIHook\n                                ),\n                            ];\n                        case 1:\n                            result = _b.sent();\n                            return [4 /*yield*/, this.getResponseJsonOrThrowGeneralError(result)];\n                        case 2:\n                            jsonBody = _b.sent();\n                            return [\n                                2 /*return*/,\n                                {\n                                    jsonBody: jsonBody,\n                                    fetchResponse: result,\n                                },\n                            ];\n                    }\n                });\n            });\n        };\n        this.post = function (tenantId, path, config, preAPIHook, postAPIHook) {\n            return __awaiter(_this, void 0, void 0, function () {\n                var result, jsonBody;\n                return __generator(this, function (_b) {\n                    switch (_b.label) {\n                        case 0:\n                            if (config.body === undefined) {\n                                throw new Error(\"Post request must have a body\");\n                            }\n                            return [\n                                4 /*yield*/,\n                                this.fetch(\n                                    this.getFullUrl(tenantId, path),\n                                    __assign({ method: \"POST\" }, config),\n                                    preAPIHook,\n                                    postAPIHook\n                                ),\n                            ];\n                        case 1:\n                            result = _b.sent();\n                            return [4 /*yield*/, this.getResponseJsonOrThrowGeneralError(result)];\n                        case 2:\n                            jsonBody = _b.sent();\n                            return [\n                                2 /*return*/,\n                                {\n                                    jsonBody: jsonBody,\n                                    fetchResponse: result,\n                                },\n                            ];\n                    }\n                });\n            });\n        };\n        this.delete = function (tenantId, path, config, preAPIHook, postAPIHook) {\n            return __awaiter(_this, void 0, void 0, function () {\n                var result, jsonBody;\n                return __generator(this, function (_b) {\n                    switch (_b.label) {\n                        case 0:\n                            return [\n                                4 /*yield*/,\n                                this.fetch(\n                                    this.getFullUrl(tenantId, path),\n                                    __assign({ method: \"DELETE\" }, config),\n                                    preAPIHook,\n                                    postAPIHook\n                                ),\n                            ];\n                        case 1:\n                            result = _b.sent();\n                            return [4 /*yield*/, this.getResponseJsonOrThrowGeneralError(result)];\n                        case 2:\n                            jsonBody = _b.sent();\n                            return [\n                                2 /*return*/,\n                                {\n                                    jsonBody: jsonBody,\n                                    fetchResponse: result,\n                                },\n                            ];\n                    }\n                });\n            });\n        };\n        this.put = function (tenantId, path, config, preAPIHook, postAPIHook) {\n            return __awaiter(_this, void 0, void 0, function () {\n                var result, jsonBody;\n                return __generator(this, function (_b) {\n                    switch (_b.label) {\n                        case 0:\n                            return [\n                                4 /*yield*/,\n                                this.fetch(\n                                    this.getFullUrl(tenantId, path),\n                                    __assign({ method: \"PUT\" }, config),\n                                    preAPIHook,\n                                    postAPIHook\n                                ),\n                            ];\n                        case 1:\n                            result = _b.sent();\n                            return [4 /*yield*/, this.getResponseJsonOrThrowGeneralError(result)];\n                        case 2:\n                            jsonBody = _b.sent();\n                            return [\n                                2 /*return*/,\n                                {\n                                    jsonBody: jsonBody,\n                                    fetchResponse: result,\n                                },\n                            ];\n                    }\n                });\n            });\n        };\n        this.fetch = function (url, config, preAPIHook, postAPIHook) {\n            return __awaiter(_this, void 0, void 0, function () {\n                var headers, _b, requestInit, modifiedUrl, result, reponseForPostAPI;\n                return __generator(this, function (_c) {\n                    switch (_c.label) {\n                        case 0:\n                            if (config === undefined) {\n                                headers = {};\n                            } else {\n                                headers = config.headers;\n                            }\n                            return [\n                                4 /*yield*/,\n                                this.callPreAPIHook({\n                                    preAPIHook: preAPIHook,\n                                    url: url,\n                                    requestInit: __assign(__assign({}, config), {\n                                        headers: __assign(__assign({}, headers), {\n                                            \"fdi-version\": version_1.supported_fdi.join(\",\"),\n                                            \"Content-Type\": \"application/json\",\n                                            rid: this.recipeId,\n                                        }),\n                                    }),\n                                }),\n                            ];\n                        case 1:\n                            (_b = _c.sent()), (requestInit = _b.requestInit), (modifiedUrl = _b.url);\n                            return [4 /*yield*/, fetch(modifiedUrl, requestInit)];\n                        case 2:\n                            result = _c.sent();\n                            if (result.status >= 300) {\n                                throw result;\n                            }\n                            if (!(postAPIHook !== undefined)) return [3 /*break*/, 4];\n                            reponseForPostAPI = result.clone();\n                            return [\n                                4 /*yield*/,\n                                postAPIHook({\n                                    requestInit: requestInit,\n                                    url: url,\n                                    fetchResponse: reponseForPostAPI,\n                                }),\n                            ];\n                        case 3:\n                            _c.sent();\n                            _c.label = 4;\n                        case 4:\n                            return [2 /*return*/, result];\n                    }\n                });\n            });\n        };\n        /*\n         * For backward compatibility\n         */\n        this.callPreAPIHook = function (context) {\n            return __awaiter(_this, void 0, void 0, function () {\n                var result;\n                return __generator(this, function (_b) {\n                    switch (_b.label) {\n                        case 0:\n                            if (context.preAPIHook === undefined) {\n                                return [\n                                    2 /*return*/,\n                                    {\n                                        url: context.url,\n                                        requestInit: context.requestInit,\n                                    },\n                                ];\n                            }\n                            return [\n                                4 /*yield*/,\n                                context.preAPIHook({\n                                    url: context.url,\n                                    requestInit: context.requestInit,\n                                }),\n                            ];\n                        case 1:\n                            result = _b.sent();\n                            return [2 /*return*/, result];\n                    }\n                });\n            });\n        };\n        this.getFullUrl = function (tenantId, pathStr, queryParams) {\n            var basePath = _this.appInfo.apiBasePath.getAsStringDangerous();\n            if (tenantId !== undefined && tenantId !== \"public\") {\n                basePath = \"\".concat(basePath, \"/\").concat(tenantId);\n            }\n            var path = new normalisedURLPath_1.default(pathStr);\n            var fullUrl = \"\"\n                .concat(_this.appInfo.apiDomain.getAsStringDangerous())\n                .concat(basePath)\n                .concat(path.getAsStringDangerous());\n            if (queryParams === undefined) {\n                return fullUrl;\n            }\n            // If query params, add.\n            return fullUrl + \"?\" + new URLSearchParams(queryParams);\n        };\n        this.getResponseJsonOrThrowGeneralError = function (response) {\n            return __awaiter(_this, void 0, void 0, function () {\n                var json, message;\n                return __generator(this, function (_b) {\n                    switch (_b.label) {\n                        case 0:\n                            return [4 /*yield*/, response.clone().json()];\n                        case 1:\n                            json = _b.sent();\n                            if (json.status === \"GENERAL_ERROR\") {\n                                message = json.message === undefined ? \"No Error Message Provided\" : json.message;\n                                throw new error_1.default(message);\n                            }\n                            return [2 /*return*/, json];\n                    }\n                });\n            });\n        };\n    }\n    var _a;\n    _a = Querier;\n    Querier.preparePreAPIHook = function (_b) {\n        var recipePreAPIHook = _b.recipePreAPIHook,\n            action = _b.action,\n            options = _b.options,\n            userContext = _b.userContext;\n        return function (context) {\n            return __awaiter(void 0, void 0, void 0, function () {\n                var postRecipeHookContext;\n                return __generator(_a, function (_b) {\n                    switch (_b.label) {\n                        case 0:\n                            return [\n                                4 /*yield*/,\n                                recipePreAPIHook(\n                                    __assign(__assign({}, context), { action: action, userContext: userContext })\n                                ),\n                            ];\n                        case 1:\n                            postRecipeHookContext = _b.sent();\n                            if (options === undefined || options.preAPIHook === undefined) {\n                                return [2 /*return*/, postRecipeHookContext];\n                            }\n                            return [\n                                2 /*return*/,\n                                options.preAPIHook({\n                                    url: postRecipeHookContext.url,\n                                    requestInit: postRecipeHookContext.requestInit,\n                                    userContext: userContext,\n                                }),\n                            ];\n                    }\n                });\n            });\n        };\n    };\n    Querier.preparePostAPIHook = function (_b) {\n        var recipePostAPIHook = _b.recipePostAPIHook,\n            action = _b.action,\n            userContext = _b.userContext;\n        return function (context) {\n            return __awaiter(void 0, void 0, void 0, function () {\n                return __generator(_a, function (_b) {\n                    switch (_b.label) {\n                        case 0:\n                            return [\n                                4 /*yield*/,\n                                recipePostAPIHook(\n                                    __assign(__assign({}, context), { userContext: userContext, action: action })\n                                ),\n                            ];\n                        case 1:\n                            _b.sent();\n                            return [2 /*return*/];\n                    }\n                });\n            });\n        };\n    };\n    return Querier;\n})();\nexports.default = Querier;\n", "\"use strict\";\n/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\nvar __awaiter =\n    (this && this.__awaiter) ||\n    function (thisArg, _arguments, P, generator) {\n        function adopt(value) {\n            return value instanceof P\n                ? value\n                : new P(function (resolve) {\n                      resolve(value);\n                  });\n        }\n        return new (P || (P = Promise))(function (resolve, reject) {\n            function fulfilled(value) {\n                try {\n                    step(generator.next(value));\n                } catch (e) {\n                    reject(e);\n                }\n            }\n            function rejected(value) {\n                try {\n                    step(generator[\"throw\"](value));\n                } catch (e) {\n                    reject(e);\n                }\n            }\n            function step(result) {\n                result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n            }\n            step((generator = generator.apply(thisArg, _arguments || [])).next());\n        });\n    };\nvar __generator =\n    (this && this.__generator) ||\n    function (thisArg, body) {\n        var _ = {\n                label: 0,\n                sent: function () {\n                    if (t[0] & 1) throw t[1];\n                    return t[1];\n                },\n                trys: [],\n                ops: [],\n            },\n            f,\n            y,\n            t,\n            g;\n        return (\n            (g = { next: verb(0), throw: verb(1), return: verb(2) }),\n            typeof Symbol === \"function\" &&\n                (g[Symbol.iterator] = function () {\n                    return this;\n                }),\n            g\n        );\n        function verb(n) {\n            return function (v) {\n                return step([n, v]);\n            };\n        }\n        function step(op) {\n            if (f) throw new TypeError(\"Generator is already executing.\");\n            while (_)\n                try {\n                    if (\n                        ((f = 1),\n                        y &&\n                            (t =\n                                op[0] & 2\n                                    ? y[\"return\"]\n                                    : op[0]\n                                    ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0)\n                                    : y.next) &&\n                            !(t = t.call(y, op[1])).done)\n                    )\n                        return t;\n                    if (((y = 0), t)) op = [op[0] & 2, t.value];\n                    switch (op[0]) {\n                        case 0:\n                        case 1:\n                            t = op;\n                            break;\n                        case 4:\n                            _.label++;\n                            return { value: op[1], done: false };\n                        case 5:\n                            _.label++;\n                            y = op[1];\n                            op = [0];\n                            continue;\n                        case 7:\n                            op = _.ops.pop();\n                            _.trys.pop();\n                            continue;\n                        default:\n                            if (\n                                !((t = _.trys), (t = t.length > 0 && t[t.length - 1])) &&\n                                (op[0] === 6 || op[0] === 2)\n                            ) {\n                                _ = 0;\n                                continue;\n                            }\n                            if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) {\n                                _.label = op[1];\n                                break;\n                            }\n                            if (op[0] === 6 && _.label < t[1]) {\n                                _.label = t[1];\n                                t = op;\n                                break;\n                            }\n                            if (t && _.label < t[2]) {\n                                _.label = t[2];\n                                _.ops.push(op);\n                                break;\n                            }\n                            if (t[2]) _.ops.pop();\n                            _.trys.pop();\n                            continue;\n                    }\n                    op = body.call(thisArg, _);\n                } catch (e) {\n                    op = [6, e];\n                    y = 0;\n                } finally {\n                    f = t = 0;\n                }\n            if (op[0] & 5) throw op[1];\n            return { value: op[0] ? op[1] : void 0, done: true };\n        }\n    };\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getRecipeImplementation = void 0;\nvar querier_1 = require(\"../../querier\");\nvar utils_1 = require(\"../../utils\");\nfunction getRecipeImplementation(recipeImplInput) {\n    var querier = new querier_1.default(recipeImplInput.recipeId, recipeImplInput.appInfo);\n    return {\n        getTenantId: function () {\n            var queryParam = (0, utils_1.getQueryParams)(\"tenantId\");\n            if ((queryParam === null || queryParam === void 0 ? void 0 : queryParam.trim()) === \"\") {\n                return undefined; // This defaults to the \"public\" tenant\n            }\n            return queryParam;\n        },\n        getLoginMethods: function (_a) {\n            var tenantId = _a.tenantId,\n                options = _a.options,\n                userContext = _a.userContext;\n            return __awaiter(this, void 0, void 0, function () {\n                var queryParams, _b, jsonBody, fetchResponse, firstFactors;\n                return __generator(this, function (_c) {\n                    switch (_c.label) {\n                        case 0:\n                            queryParams = {};\n                            if (recipeImplInput.clientType !== undefined) {\n                                queryParams.clientType = recipeImplInput.clientType;\n                            }\n                            return [\n                                4 /*yield*/,\n                                querier.get(\n                                    tenantId,\n                                    \"/loginmethods\",\n                                    {},\n                                    queryParams,\n                                    querier_1.default.preparePreAPIHook({\n                                        recipePreAPIHook: recipeImplInput.preAPIHook,\n                                        action: \"GET_LOGIN_METHODS\",\n                                        options: options,\n                                        userContext: userContext,\n                                    }),\n                                    querier_1.default.preparePostAPIHook({\n                                        recipePostAPIHook: recipeImplInput.postAPIHook,\n                                        action: \"GET_LOGIN_METHODS\",\n                                        userContext: userContext,\n                                    })\n                                ),\n                            ];\n                        case 1:\n                            (_b = _c.sent()), (jsonBody = _b.jsonBody), (fetchResponse = _b.fetchResponse);\n                            if (jsonBody.firstFactors === undefined) {\n                                firstFactors = [];\n                                if (jsonBody.emailPassword.enabled) {\n                                    firstFactors.push(\"emailpassword\");\n                                }\n                                if (jsonBody.thirdParty.enabled) {\n                                    firstFactors.push(\"thirdparty\");\n                                }\n                                if (jsonBody.passwordless.enabled) {\n                                    firstFactors.push(\"otp-email\");\n                                    firstFactors.push(\"otp-phone\");\n                                    firstFactors.push(\"link-email\");\n                                    firstFactors.push(\"link-phone\");\n                                }\n                            } else {\n                                firstFactors = jsonBody.firstFactors;\n                            }\n                            return [\n                                2 /*return*/,\n                                {\n                                    status: \"OK\",\n                                    thirdParty: {\n                                        providers: jsonBody.thirdParty.providers,\n                                    },\n                                    firstFactors: firstFactors,\n                                    fetchResponse: fetchResponse,\n                                },\n                            ];\n                    }\n                });\n            });\n        },\n    };\n}\nexports.default = getRecipeImplementation;\nexports.getRecipeImplementation = getRecipeImplementation;\n", "\"use strict\";\n/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\nvar __extends =\n    (this && this.__extends) ||\n    (function () {\n        var extendStatics = function (d, b) {\n            extendStatics =\n                Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array &&\n                    function (d, b) {\n                        d.__proto__ = b;\n                    }) ||\n                function (d, b) {\n                    for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n                };\n            return extendStatics(d, b);\n        };\n        return function (d, b) {\n            if (typeof b !== \"function\" && b !== null)\n                throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n            extendStatics(d, b);\n            function __() {\n                this.constructor = d;\n            }\n            d.prototype = b === null ? Object.create(b) : ((__.prototype = b.prototype), new __());\n        };\n    })();\nvar __awaiter =\n    (this && this.__awaiter) ||\n    function (thisArg, _arguments, P, generator) {\n        function adopt(value) {\n            return value instanceof P\n                ? value\n                : new P(function (resolve) {\n                      resolve(value);\n                  });\n        }\n        return new (P || (P = Promise))(function (resolve, reject) {\n            function fulfilled(value) {\n                try {\n                    step(generator.next(value));\n                } catch (e) {\n                    reject(e);\n                }\n            }\n            function rejected(value) {\n                try {\n                    step(generator[\"throw\"](value));\n                } catch (e) {\n                    reject(e);\n                }\n            }\n            function step(result) {\n                result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n            }\n            step((generator = generator.apply(thisArg, _arguments || [])).next());\n        });\n    };\nvar __generator =\n    (this && this.__generator) ||\n    function (thisArg, body) {\n        var _ = {\n                label: 0,\n                sent: function () {\n                    if (t[0] & 1) throw t[1];\n                    return t[1];\n                },\n                trys: [],\n                ops: [],\n            },\n            f,\n            y,\n            t,\n            g;\n        return (\n            (g = { next: verb(0), throw: verb(1), return: verb(2) }),\n            typeof Symbol === \"function\" &&\n                (g[Symbol.iterator] = function () {\n                    return this;\n                }),\n            g\n        );\n        function verb(n) {\n            return function (v) {\n                return step([n, v]);\n            };\n        }\n        function step(op) {\n            if (f) throw new TypeError(\"Generator is already executing.\");\n            while (_)\n                try {\n                    if (\n                        ((f = 1),\n                        y &&\n                            (t =\n                                op[0] & 2\n                                    ? y[\"return\"]\n                                    : op[0]\n                                    ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0)\n                                    : y.next) &&\n                            !(t = t.call(y, op[1])).done)\n                    )\n                        return t;\n                    if (((y = 0), t)) op = [op[0] & 2, t.value];\n                    switch (op[0]) {\n                        case 0:\n                        case 1:\n                            t = op;\n                            break;\n                        case 4:\n                            _.label++;\n                            return { value: op[1], done: false };\n                        case 5:\n                            _.label++;\n                            y = op[1];\n                            op = [0];\n                            continue;\n                        case 7:\n                            op = _.ops.pop();\n                            _.trys.pop();\n                            continue;\n                        default:\n                            if (\n                                !((t = _.trys), (t = t.length > 0 && t[t.length - 1])) &&\n                                (op[0] === 6 || op[0] === 2)\n                            ) {\n                                _ = 0;\n                                continue;\n                            }\n                            if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) {\n                                _.label = op[1];\n                                break;\n                            }\n                            if (op[0] === 6 && _.label < t[1]) {\n                                _.label = t[1];\n                                t = op;\n                                break;\n                            }\n                            if (t && _.label < t[2]) {\n                                _.label = t[2];\n                                _.ops.push(op);\n                                break;\n                            }\n                            if (t[2]) _.ops.pop();\n                            _.trys.pop();\n                            continue;\n                    }\n                    op = body.call(thisArg, _);\n                } catch (e) {\n                    op = [6, e];\n                    y = 0;\n                } finally {\n                    f = t = 0;\n                }\n            if (op[0] & 5) throw op[1];\n            return { value: op[0] ? op[1] : void 0, done: true };\n        }\n    };\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar recipeModule_1 = require(\"../recipeModule\");\nvar recipe_1 = require(\"../session/recipe\");\nvar AuthRecipe = /** @class */ (function (_super) {\n    __extends(AuthRecipe, _super);\n    function AuthRecipe(config) {\n        var _this = _super.call(this, config) || this;\n        _this.signOut = function (input) {\n            return __awaiter(_this, void 0, void 0, function () {\n                return __generator(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            return [\n                                4 /*yield*/,\n                                recipe_1.default.getInstanceOrThrow().signOut({\n                                    userContext: input.userContext,\n                                }),\n                            ];\n                        case 1:\n                            return [2 /*return*/, _a.sent()];\n                    }\n                });\n            });\n        };\n        return _this;\n    }\n    return AuthRecipe;\n})(recipeModule_1.default);\nexports.default = AuthRecipe;\n", "\"use strict\";\n/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\nvar __extends =\n    (this && this.__extends) ||\n    (function () {\n        var extendStatics = function (d, b) {\n            extendStatics =\n                Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array &&\n                    function (d, b) {\n                        d.__proto__ = b;\n                    }) ||\n                function (d, b) {\n                    for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n                };\n            return extendStatics(d, b);\n        };\n        return function (d, b) {\n            if (typeof b !== \"function\" && b !== null)\n                throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n            extendStatics(d, b);\n            function __() {\n                this.constructor = d;\n            }\n            d.prototype = b === null ? Object.create(b) : ((__.prototype = b.prototype), new __());\n        };\n    })();\nvar __assign =\n    (this && this.__assign) ||\n    function () {\n        __assign =\n            Object.assign ||\n            function (t) {\n                for (var s, i = 1, n = arguments.length; i < n; i++) {\n                    s = arguments[i];\n                    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n                }\n                return t;\n            };\n        return __assign.apply(this, arguments);\n    };\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Recipe = void 0;\nvar utils_1 = require(\"./utils\");\nvar supertokens_js_override_1 = require(\"supertokens-js-override\");\nvar recipeImplementation_1 = require(\"./recipeImplementation\");\nvar utils_2 = require(\"../../utils\");\nvar authRecipe_1 = require(\"../authRecipe\");\nvar Recipe = /** @class */ (function (_super) {\n    __extends(Recipe, _super);\n    function Recipe(config) {\n        var _this = _super.call(this, (0, utils_1.normaliseUserInput)(config)) || this;\n        var builder = new supertokens_js_override_1.default(\n            (0, recipeImplementation_1.default)({\n                recipeId: _this.config.recipeId,\n                appInfo: _this.config.appInfo,\n                clientType: _this.config.clientType,\n                preAPIHook: _this.config.preAPIHook,\n                postAPIHook: _this.config.postAPIHook,\n            })\n        );\n        _this.recipeImplementation = builder.override(_this.config.override.functions).build();\n        return _this;\n    }\n    Recipe.init = function (config) {\n        return function (appInfo, clientType) {\n            Recipe.instance = new Recipe(\n                __assign(__assign({}, config), { recipeId: Recipe.RECIPE_ID, appInfo: appInfo, clientType: clientType })\n            );\n            return Recipe.instance;\n        };\n    };\n    Recipe.getInstanceOrThrow = function () {\n        if (Recipe.instance === undefined) {\n            var error = \"No instance of Multitenancy found. Ensure that 'SuperTokens.init' method has been called.\";\n            error = (0, utils_2.checkForSSRErrorAndAppendIfNeeded)(error);\n            throw Error(error);\n        }\n        return Recipe.instance;\n    };\n    Recipe.reset = function () {\n        if (!(0, utils_2.isTest)()) {\n            return;\n        }\n        Recipe.instance = undefined;\n        return;\n    };\n    Recipe.RECIPE_ID = \"multitenancy\";\n    return Recipe;\n})(authRecipe_1.default);\nexports.Recipe = Recipe;\nexports.default = Recipe;\n", "/* Copyright (c) 2020, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n * \n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n * \n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License. \n*/\n\"use strict\";\nfunction __export(m) {\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\n}\nexports.__esModule = true;\n__export(require(\"../../lib/build/utils/dateProvider\"));\n", "\"use strict\";\n/* Copyright (c) 2024, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DateProviderReference = void 0;\nvar dateProvider_1 = require(\"supertokens-website/utils/dateProvider\");\nObject.defineProperty(exports, \"DateProviderReference\", {\n    enumerable: true,\n    get: function () {\n        return dateProvider_1.DateProviderReference;\n    },\n});\n", "\"use strict\";\n/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar utils_1 = require(\"./utils\");\nvar cookieHandler_1 = require(\"./cookieHandler\");\nvar windowHandler_1 = require(\"./windowHandler\");\nvar postSuperTokensInitCallbacks_1 = require(\"./postSuperTokensInitCallbacks\");\nvar recipe_1 = require(\"./recipe/multitenancy/recipe\");\nvar dateProvider_1 = require(\"./dateProvider\");\nvar SuperTokens = /** @class */ (function () {\n    function SuperTokens(config) {\n        var _this = this;\n        this.recipeList = [];\n        this.appInfo = (0, utils_1.normaliseInputAppInfoOrThrowError)(config.appInfo);\n        if (config.recipeList === undefined || config.recipeList.length === 0) {\n            throw new Error(\n                \"Please provide at least one recipe to the supertokens.init function call. See https://supertokens.io/docs/emailpassword/quick-setup/frontend\"\n            );\n        }\n        var enableDebugLogs = false;\n        if (config.enableDebugLogs !== undefined) {\n            enableDebugLogs = config.enableDebugLogs;\n        }\n        var multitenancyFound = false;\n        this.recipeList = config.recipeList.map(function (recipe) {\n            var recipeInstance = recipe(_this.appInfo, config.clientType, enableDebugLogs);\n            if (recipeInstance.config.recipeId === recipe_1.Recipe.RECIPE_ID) {\n                multitenancyFound = true;\n            }\n            return recipeInstance;\n        });\n        if (!multitenancyFound) {\n            this.recipeList.push(recipe_1.Recipe.init()(this.appInfo, config.clientType, enableDebugLogs));\n        }\n    }\n    /**\n     * Initialise the SuperTokens SDK. Calling this function multiple times results\n     * in a warning and has no other effect\n     *\n     * @param config The configuration the SDK should use\n     */\n    SuperTokens.init = function (config) {\n        cookieHandler_1.CookieHandlerReference.init(config.cookieHandler);\n        windowHandler_1.WindowHandlerReference.init(config.windowHandler);\n        dateProvider_1.DateProviderReference.init(config.dateProvider);\n        if (SuperTokens.instance !== undefined) {\n            console.warn(\"SuperTokens was already initialized\");\n            return;\n        }\n        SuperTokens.instance = new SuperTokens(config);\n        postSuperTokensInitCallbacks_1.PostSuperTokensInitCallbacks.runPostInitCallbacks();\n    };\n    /**\n     * Retrieve an instance of SuperTokens\n     *\n     * @returns An instance of SuperTokens\n     *\n     * @throws If SuperTokens.init has not been called before using this function\n     */\n    SuperTokens.getInstanceOrThrow = function () {\n        if (SuperTokens.instance === undefined) {\n            var error = \"SuperTokens must be initialized before calling this method.\";\n            error = (0, utils_1.checkForSSRErrorAndAppendIfNeeded)(error);\n            throw new Error(error);\n        }\n        return SuperTokens.instance;\n    };\n    SuperTokens.reset = function () {\n        if (!(0, utils_1.isTest)()) {\n            console.warn(\"Calling reset() is only supported during testing\");\n            return;\n        }\n        // We reset the multitenancy recipe here because we are auto-initializing it\n        // and we should always be resetting it when we reset the SDK\n        recipe_1.Recipe.reset();\n        SuperTokens.instance = undefined;\n        return;\n    };\n    return SuperTokens;\n})();\nexports.default = SuperTokens;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.init = void 0;\n/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\nvar supertokens_1 = require(\"./supertokens\");\nvar SuperTokensAPIWrapper = /** @class */ (function () {\n    function SuperTokensAPIWrapper() {}\n    SuperTokensAPIWrapper.init = function (config) {\n        supertokens_1.default.init(config);\n    };\n    return SuperTokensAPIWrapper;\n})();\nexports.default = SuperTokensAPIWrapper;\nexports.init = SuperTokensAPIWrapper.init;\n", "/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\n\n\"use strict\";\nfunction __export(m) {\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\n}\nexports.__esModule = true;\n__export(require(\"./lib/build/\"));\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,yBAAA;AAAA;AAAA;AAeA,aAAS,SAAS,GAAG;AACjB,eAAS,KAAK,EAAG,KAAI,CAAC,QAAQ,eAAe,CAAC,EAAG,SAAQ,CAAC,IAAI,EAAE,CAAC;AAAA,IACrE;AACA,YAAQ,aAAa;AACrB,aAAS,uBAA8C;AAAA;AAAA;;;ACnBvD,IAAAC,yBAAA;AAAA;AAAA;AAeA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,yBAAyB;AACjC,QAAI,kBAAkB;AACtB,WAAO,eAAe,SAAS,0BAA0B;AAAA,MACrD,YAAY;AAAA,MACZ,KAAK,WAAY;AACb,eAAO,gBAAgB;AAAA,MAC3B;AAAA,IACJ,CAAC;AAAA;AAAA;;;ACvBD;AAAA;AAAA;AAeA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,+BAA+B;AACvC,QAAI;AAAA;AAAA,MAA8C,WAAY;AAC1D,iBAASC,gCAA+B;AAAA,QAAC;AACzC,QAAAA,8BAA6B,sBAAsB,SAAU,IAAI;AAC7D,UAAAA,8BAA6B,kBAAkB,KAAK,EAAE;AAAA,QAC1D;AACA,QAAAA,8BAA6B,uBAAuB,WAAY;AAC5D,mBAAS,KAAK,GAAG,KAAKA,8BAA6B,mBAAmB,KAAK,GAAG,QAAQ,MAAM;AACxF,gBAAI,KAAK,GAAG,EAAE;AACd,eAAG;AAAA,UACP;AAAA,QACJ;AACA,QAAAA,8BAA6B,oBAAoB,CAAC;AAClD,eAAOA;AAAA,MACX,EAAG;AAAA;AACH,YAAQ,+BAA+B;AAAA;AAAA;;;AC/BvC,IAAAC,iBAAA;AAAA;AAAA;AACA,QAAI,YACC,WAAQ,QAAK,aACd,SAAU,SAAS,YAAY,GAAG,WAAW;AACzC,eAAS,MAAM,OAAO;AAClB,eAAO,iBAAiB,IAClB,QACA,IAAI,EAAE,SAAU,SAAS;AACrB,kBAAQ,KAAK;AAAA,QACjB,CAAC;AAAA,MACX;AACA,aAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,iBAAS,UAAU,OAAO;AACtB,cAAI;AACA,iBAAK,UAAU,KAAK,KAAK,CAAC;AAAA,UAC9B,SAAS,GAAG;AACR,mBAAO,CAAC;AAAA,UACZ;AAAA,QACJ;AACA,iBAAS,SAAS,OAAO;AACrB,cAAI;AACA,iBAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,UAClC,SAAS,GAAG;AACR,mBAAO,CAAC;AAAA,UACZ;AAAA,QACJ;AACA,iBAAS,KAAK,QAAQ;AAClB,iBAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,QACtF;AACA,cAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,MACxE,CAAC;AAAA,IACL;AACJ,QAAI,cACC,WAAQ,QAAK,eACd,SAAU,SAAS,MAAM;AACrB,UAAI,IAAI;AAAA,QACA,OAAO;AAAA,QACP,MAAM,WAAY;AACd,cAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AACvB,iBAAO,EAAE,CAAC;AAAA,QACd;AAAA,QACA,MAAM,CAAC;AAAA,QACP,KAAK,CAAC;AAAA,MACV,GACA,GACA,GACA,GACA;AACJ,aACK,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,QAAQ,KAAK,CAAC,EAAE,GACtD,OAAO,WAAW,eACb,EAAE,OAAO,QAAQ,IAAI,WAAY;AAC9B,eAAO;AAAA,MACX,IACJ;AAEJ,eAAS,KAAK,GAAG;AACb,eAAO,SAAU,GAAG;AAChB,iBAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,QACtB;AAAA,MACJ;AACA,eAAS,KAAK,IAAI;AACd,YAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,eAAO;AACH,cAAI;AACA,gBACM,IAAI,GACN,MACK,IACG,GAAG,CAAC,IAAI,IACF,EAAE,QAAQ,IACV,GAAG,CAAC,IACJ,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAC/C,EAAE,SACZ,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAE5B,qBAAO;AACX,gBAAM,IAAI,GAAI,EAAI,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AAC1C,oBAAQ,GAAG,CAAC,GAAG;AAAA,cACX,KAAK;AAAA,cACL,KAAK;AACD,oBAAI;AACJ;AAAA,cACJ,KAAK;AACD,kBAAE;AACF,uBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,cACvC,KAAK;AACD,kBAAE;AACF,oBAAI,GAAG,CAAC;AACR,qBAAK,CAAC,CAAC;AACP;AAAA,cACJ,KAAK;AACD,qBAAK,EAAE,IAAI,IAAI;AACf,kBAAE,KAAK,IAAI;AACX;AAAA,cACJ;AACI,oBACI,EAAG,IAAI,EAAE,MAAQ,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAClD,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAC5B;AACE,sBAAI;AACJ;AAAA,gBACJ;AACA,oBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AACvD,oBAAE,QAAQ,GAAG,CAAC;AACd;AAAA,gBACJ;AACA,oBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAC/B,oBAAE,QAAQ,EAAE,CAAC;AACb,sBAAI;AACJ;AAAA,gBACJ;AACA,oBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AACrB,oBAAE,QAAQ,EAAE,CAAC;AACb,oBAAE,IAAI,KAAK,EAAE;AACb;AAAA,gBACJ;AACA,oBAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,kBAAE,KAAK,IAAI;AACX;AAAA,YACR;AACA,iBAAK,KAAK,KAAK,SAAS,CAAC;AAAA,UAC7B,SAAS,GAAG;AACR,iBAAK,CAAC,GAAG,CAAC;AACV,gBAAI;AAAA,UACR,UAAE;AACE,gBAAI,IAAI;AAAA,UACZ;AACJ,YAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AACzB,eAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,MACvD;AAAA,IACJ;AACJ,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,8BAA8B;AACtC,aAAS,4BAA4B,QAAQ;AACzC,UAAI,QAAQ;AACZ,UAAI,aAAa,OAAO;AACxB,UAAI,eAAe,QAAW;AAC1B,qBAAa,SAAU,SAAS;AAC5B,iBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAChD,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,qBAAO,CAAC,GAAc,OAAO;AAAA,YACjC,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AAAA,MACJ;AACA,UAAI,cAAc,OAAO;AACzB,UAAI,gBAAgB,QAAW;AAC3B,sBAAc,WAAY;AACtB,iBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAChD,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,YACxB,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AAAA,MACJ;AACA,aAAO;AAAA,QACH,UAAU,OAAO;AAAA,QACjB,SAAS,OAAO;AAAA,QAChB,YAAY,OAAO;AAAA,QACnB;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,8BAA8B;AAAA;AAAA;;;ACpKtC,IAAAC,iBAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,sBAAsB;AAe9B,QAAI,UAAU;AACd,aAAS,oBAAoB,QAAQ;AACjC,cAAQ,GAAG,QAAQ,6BAA6B,MAAM;AAAA,IAC1D;AACA,YAAQ,sBAAsB;AAAA;AAAA;;;ACrB9B,IAAAC,iBAAA;AAAA;AAAA;AACA,QAAI,WACC,WAAQ,QAAK,YACd,WAAY;AACR,iBACI,OAAO,UACP,SAAU,GAAG;AACT,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACJ,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACJ,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,qBAAqB;AAe7B,QAAI,UAAU;AACd,aAAS,mBAAmB,QAAQ;AAChC,UAAI,WAAW;AAAA,QACX;AAAA,UACI,WAAW,SAAU,wBAAwB;AACzC,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,QACA,OAAO;AAAA,MACX;AACA,aAAO,SAAS,SAAS,CAAC,IAAI,GAAG,QAAQ,qBAAqB,MAAM,CAAC,GAAG,EAAE,SAAmB,CAAC;AAAA,IAClG;AACA,YAAQ,qBAAqB;AAAA;AAAA;;;AC3C7B;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,gBAAgB,QAAQ,kBAAkB;AAelD,YAAQ,kBAAkB;AAC1B,YAAQ,gBAAgB,CAAC,OAAO,OAAO,KAAK;AAAA;AAAA;;;AClB5C,IAAAC,iBAAA;AAAA;AAAA;AAeA,aAAS,SAAS,GAAG;AACnB,eAAS,KAAK,EAAG,KAAI,CAAC,QAAQ,eAAe,CAAC,EAAG,SAAQ,CAAC,IAAI,EAAE,CAAC;AAAA,IACnE;AACA,YAAQ,aAAa;AAErB,QAAI,IAAI;AAER,QAAI,EAAE,YAAY,QAAW;AACzB,eAAS,CAAC;AAAA,IACd,OAAO;AACH,eAAS;AAAA,QACL,SAAS;AAAA,QACT,GAAG;AAAA,MACP,CAAC;AAAA,IACL;AAAA;AAAA;;;AC7BA,IAAAC,iBAAA;AAAA;AAAA;AAeA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAM5D,QAAI,UAAU;AACd,YAAQ,UAAU,QAAQ;AAAA;AAAA;;;ACtB1B;AAAA;AAAA;AACA,QAAI,WACC,WAAQ,QAAK,YACd,WAAY;AACR,iBACI,OAAO,UACP,SAAU,GAAG;AACT,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACJ,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACJ,QAAI,YACC,WAAQ,QAAK,aACd,SAAU,SAAS,YAAY,GAAG,WAAW;AACzC,eAAS,MAAM,OAAO;AAClB,eAAO,iBAAiB,IAClB,QACA,IAAI,EAAE,SAAU,SAAS;AACrB,kBAAQ,KAAK;AAAA,QACjB,CAAC;AAAA,MACX;AACA,aAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,iBAAS,UAAU,OAAO;AACtB,cAAI;AACA,iBAAK,UAAU,KAAK,KAAK,CAAC;AAAA,UAC9B,SAAS,GAAG;AACR,mBAAO,CAAC;AAAA,UACZ;AAAA,QACJ;AACA,iBAAS,SAAS,OAAO;AACrB,cAAI;AACA,iBAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,UAClC,SAAS,GAAG;AACR,mBAAO,CAAC;AAAA,UACZ;AAAA,QACJ;AACA,iBAAS,KAAK,QAAQ;AAClB,iBAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,QACtF;AACA,cAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,MACxE,CAAC;AAAA,IACL;AACJ,QAAI,cACC,WAAQ,QAAK,eACd,SAAU,SAAS,MAAM;AACrB,UAAI,IAAI;AAAA,QACA,OAAO;AAAA,QACP,MAAM,WAAY;AACd,cAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AACvB,iBAAO,EAAE,CAAC;AAAA,QACd;AAAA,QACA,MAAM,CAAC;AAAA,QACP,KAAK,CAAC;AAAA,MACV,GACA,GACA,GACA,GACA;AACJ,aACK,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,QAAQ,KAAK,CAAC,EAAE,GACtD,OAAO,WAAW,eACb,EAAE,OAAO,QAAQ,IAAI,WAAY;AAC9B,eAAO;AAAA,MACX,IACJ;AAEJ,eAAS,KAAK,GAAG;AACb,eAAO,SAAU,GAAG;AAChB,iBAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,QACtB;AAAA,MACJ;AACA,eAAS,KAAK,IAAI;AACd,YAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,eAAO;AACH,cAAI;AACA,gBACM,IAAI,GACN,MACK,IACG,GAAG,CAAC,IAAI,IACF,EAAE,QAAQ,IACV,GAAG,CAAC,IACJ,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAC/C,EAAE,SACZ,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAE5B,qBAAO;AACX,gBAAM,IAAI,GAAI,EAAI,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AAC1C,oBAAQ,GAAG,CAAC,GAAG;AAAA,cACX,KAAK;AAAA,cACL,KAAK;AACD,oBAAI;AACJ;AAAA,cACJ,KAAK;AACD,kBAAE;AACF,uBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,cACvC,KAAK;AACD,kBAAE;AACF,oBAAI,GAAG,CAAC;AACR,qBAAK,CAAC,CAAC;AACP;AAAA,cACJ,KAAK;AACD,qBAAK,EAAE,IAAI,IAAI;AACf,kBAAE,KAAK,IAAI;AACX;AAAA,cACJ;AACI,oBACI,EAAG,IAAI,EAAE,MAAQ,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAClD,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAC5B;AACE,sBAAI;AACJ;AAAA,gBACJ;AACA,oBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AACvD,oBAAE,QAAQ,GAAG,CAAC;AACd;AAAA,gBACJ;AACA,oBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAC/B,oBAAE,QAAQ,EAAE,CAAC;AACb,sBAAI;AACJ;AAAA,gBACJ;AACA,oBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AACrB,oBAAE,QAAQ,EAAE,CAAC;AACb,oBAAE,IAAI,KAAK,EAAE;AACb;AAAA,gBACJ;AACA,oBAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,kBAAE,KAAK,IAAI;AACX;AAAA,YACR;AACA,iBAAK,KAAK,KAAK,SAAS,CAAC;AAAA,UAC7B,SAAS,GAAG;AACR,iBAAK,CAAC,GAAG,CAAC;AACV,gBAAI;AAAA,UACR,UAAE;AACE,gBAAI,IAAI;AAAA,UACZ;AACJ,YAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AACzB,eAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,MACvD;AAAA,IACJ;AACJ,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAe5D,QAAI,sBAAsB;AAC1B,QAAI,YAAY;AAChB,QAAI,UAAU;AAWd,QAAI;AAAA;AAAA,MAAyB,WAAY;AACrC,iBAASC,SAAQ,UAAU,SAAS;AAChC,cAAI,QAAQ;AACZ,eAAK,WAAW;AAChB,eAAK,UAAU;AACf,eAAK,MAAM,SAAU,UAAU,MAAM,QAAQ,aAAa,YAAY,aAAa;AAC/E,mBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAChD,kBAAI,QAAQ;AACZ,qBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,wBAAQ,GAAG,OAAO;AAAA,kBACd,KAAK;AACD,2BAAO;AAAA,sBACH;AAAA,sBACA,KAAK;AAAA,wBACD,KAAK,WAAW,UAAU,MAAM,WAAW;AAAA,wBAC3C,SAAS,EAAE,QAAQ,MAAM,GAAG,MAAM;AAAA,wBAClC;AAAA,wBACA;AAAA,sBACJ;AAAA,oBACJ;AAAA,kBACJ,KAAK;AACD,6BAAS,GAAG,KAAK;AACjB,2BAAO,CAAC,GAAa,KAAK,mCAAmC,MAAM,CAAC;AAAA,kBACxE,KAAK;AACD,+BAAW,GAAG,KAAK;AACnB,2BAAO;AAAA,sBACH;AAAA,sBACA;AAAA,wBACI;AAAA,wBACA,eAAe;AAAA,sBACnB;AAAA,oBACJ;AAAA,gBACR;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UACL;AACA,eAAK,OAAO,SAAU,UAAU,MAAM,QAAQ,YAAY,aAAa;AACnE,mBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAChD,kBAAI,QAAQ;AACZ,qBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,wBAAQ,GAAG,OAAO;AAAA,kBACd,KAAK;AACD,wBAAI,OAAO,SAAS,QAAW;AAC3B,4BAAM,IAAI,MAAM,+BAA+B;AAAA,oBACnD;AACA,2BAAO;AAAA,sBACH;AAAA,sBACA,KAAK;AAAA,wBACD,KAAK,WAAW,UAAU,IAAI;AAAA,wBAC9B,SAAS,EAAE,QAAQ,OAAO,GAAG,MAAM;AAAA,wBACnC;AAAA,wBACA;AAAA,sBACJ;AAAA,oBACJ;AAAA,kBACJ,KAAK;AACD,6BAAS,GAAG,KAAK;AACjB,2BAAO,CAAC,GAAa,KAAK,mCAAmC,MAAM,CAAC;AAAA,kBACxE,KAAK;AACD,+BAAW,GAAG,KAAK;AACnB,2BAAO;AAAA,sBACH;AAAA,sBACA;AAAA,wBACI;AAAA,wBACA,eAAe;AAAA,sBACnB;AAAA,oBACJ;AAAA,gBACR;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UACL;AACA,eAAK,SAAS,SAAU,UAAU,MAAM,QAAQ,YAAY,aAAa;AACrE,mBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAChD,kBAAI,QAAQ;AACZ,qBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,wBAAQ,GAAG,OAAO;AAAA,kBACd,KAAK;AACD,2BAAO;AAAA,sBACH;AAAA,sBACA,KAAK;AAAA,wBACD,KAAK,WAAW,UAAU,IAAI;AAAA,wBAC9B,SAAS,EAAE,QAAQ,SAAS,GAAG,MAAM;AAAA,wBACrC;AAAA,wBACA;AAAA,sBACJ;AAAA,oBACJ;AAAA,kBACJ,KAAK;AACD,6BAAS,GAAG,KAAK;AACjB,2BAAO,CAAC,GAAa,KAAK,mCAAmC,MAAM,CAAC;AAAA,kBACxE,KAAK;AACD,+BAAW,GAAG,KAAK;AACnB,2BAAO;AAAA,sBACH;AAAA,sBACA;AAAA,wBACI;AAAA,wBACA,eAAe;AAAA,sBACnB;AAAA,oBACJ;AAAA,gBACR;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UACL;AACA,eAAK,MAAM,SAAU,UAAU,MAAM,QAAQ,YAAY,aAAa;AAClE,mBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAChD,kBAAI,QAAQ;AACZ,qBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,wBAAQ,GAAG,OAAO;AAAA,kBACd,KAAK;AACD,2BAAO;AAAA,sBACH;AAAA,sBACA,KAAK;AAAA,wBACD,KAAK,WAAW,UAAU,IAAI;AAAA,wBAC9B,SAAS,EAAE,QAAQ,MAAM,GAAG,MAAM;AAAA,wBAClC;AAAA,wBACA;AAAA,sBACJ;AAAA,oBACJ;AAAA,kBACJ,KAAK;AACD,6BAAS,GAAG,KAAK;AACjB,2BAAO,CAAC,GAAa,KAAK,mCAAmC,MAAM,CAAC;AAAA,kBACxE,KAAK;AACD,+BAAW,GAAG,KAAK;AACnB,2BAAO;AAAA,sBACH;AAAA,sBACA;AAAA,wBACI;AAAA,wBACA,eAAe;AAAA,sBACnB;AAAA,oBACJ;AAAA,gBACR;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UACL;AACA,eAAK,QAAQ,SAAU,KAAK,QAAQ,YAAY,aAAa;AACzD,mBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAChD,kBAAI,SAAS,IAAI,aAAa,aAAa,QAAQ;AACnD,qBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,wBAAQ,GAAG,OAAO;AAAA,kBACd,KAAK;AACD,wBAAI,WAAW,QAAW;AACtB,gCAAU,CAAC;AAAA,oBACf,OAAO;AACH,gCAAU,OAAO;AAAA,oBACrB;AACA,2BAAO;AAAA,sBACH;AAAA,sBACA,KAAK,eAAe;AAAA,wBAChB;AAAA,wBACA;AAAA,wBACA,aAAa,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG;AAAA,0BACxC,SAAS,SAAS,SAAS,CAAC,GAAG,OAAO,GAAG;AAAA,4BACrC,eAAe,UAAU,cAAc,KAAK,GAAG;AAAA,4BAC/C,gBAAgB;AAAA,4BAChB,KAAK,KAAK;AAAA,0BACd,CAAC;AAAA,wBACL,CAAC;AAAA,sBACL,CAAC;AAAA,oBACL;AAAA,kBACJ,KAAK;AACD,oBAAC,KAAK,GAAG,KAAK,GAAK,cAAc,GAAG,aAAe,cAAc,GAAG;AACpE,2BAAO,CAAC,GAAa,MAAM,aAAa,WAAW,CAAC;AAAA,kBACxD,KAAK;AACD,6BAAS,GAAG,KAAK;AACjB,wBAAI,OAAO,UAAU,KAAK;AACtB,4BAAM;AAAA,oBACV;AACA,wBAAI,EAAE,gBAAgB,QAAY,QAAO,CAAC,GAAa,CAAC;AACxD,wCAAoB,OAAO,MAAM;AACjC,2BAAO;AAAA,sBACH;AAAA,sBACA,YAAY;AAAA,wBACR;AAAA,wBACA;AAAA,wBACA,eAAe;AAAA,sBACnB,CAAC;AAAA,oBACL;AAAA,kBACJ,KAAK;AACD,uBAAG,KAAK;AACR,uBAAG,QAAQ;AAAA,kBACf,KAAK;AACD,2BAAO,CAAC,GAAc,MAAM;AAAA,gBACpC;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UACL;AAIA,eAAK,iBAAiB,SAAU,SAAS;AACrC,mBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAChD,kBAAI;AACJ,qBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,wBAAQ,GAAG,OAAO;AAAA,kBACd,KAAK;AACD,wBAAI,QAAQ,eAAe,QAAW;AAClC,6BAAO;AAAA,wBACH;AAAA,wBACA;AAAA,0BACI,KAAK,QAAQ;AAAA,0BACb,aAAa,QAAQ;AAAA,wBACzB;AAAA,sBACJ;AAAA,oBACJ;AACA,2BAAO;AAAA,sBACH;AAAA,sBACA,QAAQ,WAAW;AAAA,wBACf,KAAK,QAAQ;AAAA,wBACb,aAAa,QAAQ;AAAA,sBACzB,CAAC;AAAA,oBACL;AAAA,kBACJ,KAAK;AACD,6BAAS,GAAG,KAAK;AACjB,2BAAO,CAAC,GAAc,MAAM;AAAA,gBACpC;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UACL;AACA,eAAK,aAAa,SAAU,UAAU,SAAS,aAAa;AACxD,gBAAI,WAAW,MAAM,QAAQ,YAAY,qBAAqB;AAC9D,gBAAI,aAAa,UAAa,aAAa,UAAU;AACjD,yBAAW,GAAG,OAAO,UAAU,GAAG,EAAE,OAAO,QAAQ;AAAA,YACvD;AACA,gBAAI,OAAO,IAAI,oBAAoB,QAAQ,OAAO;AAClD,gBAAI,UAAU,GACT,OAAO,MAAM,QAAQ,UAAU,qBAAqB,CAAC,EACrD,OAAO,QAAQ,EACf,OAAO,KAAK,qBAAqB,CAAC;AACvC,gBAAI,gBAAgB,QAAW;AAC3B,qBAAO;AAAA,YACX;AAEA,mBAAO,UAAU,MAAM,IAAI,gBAAgB,WAAW;AAAA,UAC1D;AACA,eAAK,qCAAqC,SAAU,UAAU;AAC1D,mBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAChD,kBAAI,MAAM;AACV,qBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,wBAAQ,GAAG,OAAO;AAAA,kBACd,KAAK;AACD,2BAAO,CAAC,GAAa,SAAS,MAAM,EAAE,KAAK,CAAC;AAAA,kBAChD,KAAK;AACD,2BAAO,GAAG,KAAK;AACf,wBAAI,KAAK,WAAW,iBAAiB;AACjC,gCAAU,KAAK,YAAY,SAAY,8BAA8B,KAAK;AAC1E,4BAAM,IAAI,QAAQ,QAAQ,OAAO;AAAA,oBACrC;AACA,2BAAO,CAAC,GAAc,IAAI;AAAA,gBAClC;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UACL;AAAA,QACJ;AACA,YAAI;AACJ,aAAKA;AACL,QAAAA,SAAQ,oBAAoB,SAAU,IAAI;AACtC,cAAI,mBAAmB,GAAG,kBACtB,SAAS,GAAG,QACZ,UAAU,GAAG,SACb,cAAc,GAAG;AACrB,iBAAO,SAAU,SAAS;AACtB,mBAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AACjD,kBAAI;AACJ,qBAAO,YAAY,IAAI,SAAUC,KAAI;AACjC,wBAAQA,IAAG,OAAO;AAAA,kBACd,KAAK;AACD,2BAAO;AAAA,sBACH;AAAA,sBACA;AAAA,wBACI,SAAS,SAAS,CAAC,GAAG,OAAO,GAAG,EAAE,QAAgB,YAAyB,CAAC;AAAA,sBAChF;AAAA,oBACJ;AAAA,kBACJ,KAAK;AACD,4CAAwBA,IAAG,KAAK;AAChC,wBAAI,YAAY,UAAa,QAAQ,eAAe,QAAW;AAC3D,6BAAO,CAAC,GAAc,qBAAqB;AAAA,oBAC/C;AACA,2BAAO;AAAA,sBACH;AAAA,sBACA,QAAQ,WAAW;AAAA,wBACf,KAAK,sBAAsB;AAAA,wBAC3B,aAAa,sBAAsB;AAAA,wBACnC;AAAA,sBACJ,CAAC;AAAA,oBACL;AAAA,gBACR;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UACL;AAAA,QACJ;AACA,QAAAD,SAAQ,qBAAqB,SAAU,IAAI;AACvC,cAAI,oBAAoB,GAAG,mBACvB,SAAS,GAAG,QACZ,cAAc,GAAG;AACrB,iBAAO,SAAU,SAAS;AACtB,mBAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAY;AACjD,qBAAO,YAAY,IAAI,SAAUC,KAAI;AACjC,wBAAQA,IAAG,OAAO;AAAA,kBACd,KAAK;AACD,2BAAO;AAAA,sBACH;AAAA,sBACA;AAAA,wBACI,SAAS,SAAS,CAAC,GAAG,OAAO,GAAG,EAAE,aAA0B,OAAe,CAAC;AAAA,sBAChF;AAAA,oBACJ;AAAA,kBACJ,KAAK;AACD,oBAAAA,IAAG,KAAK;AACR,2BAAO;AAAA,sBAAC;AAAA;AAAA,oBAAY;AAAA,gBAC5B;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UACL;AAAA,QACJ;AACA,eAAOD;AAAA,MACX,EAAG;AAAA;AACH,YAAQ,UAAU;AAAA;AAAA;;;ACvelB;AAAA;AAAA;AAeA,QAAI,YACC,WAAQ,QAAK,aACd,SAAU,SAAS,YAAY,GAAG,WAAW;AACzC,eAAS,MAAM,OAAO;AAClB,eAAO,iBAAiB,IAClB,QACA,IAAI,EAAE,SAAU,SAAS;AACrB,kBAAQ,KAAK;AAAA,QACjB,CAAC;AAAA,MACX;AACA,aAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,iBAAS,UAAU,OAAO;AACtB,cAAI;AACA,iBAAK,UAAU,KAAK,KAAK,CAAC;AAAA,UAC9B,SAAS,GAAG;AACR,mBAAO,CAAC;AAAA,UACZ;AAAA,QACJ;AACA,iBAAS,SAAS,OAAO;AACrB,cAAI;AACA,iBAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,UAClC,SAAS,GAAG;AACR,mBAAO,CAAC;AAAA,UACZ;AAAA,QACJ;AACA,iBAAS,KAAK,QAAQ;AAClB,iBAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,QACtF;AACA,cAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,MACxE,CAAC;AAAA,IACL;AACJ,QAAI,cACC,WAAQ,QAAK,eACd,SAAU,SAAS,MAAM;AACrB,UAAI,IAAI;AAAA,QACA,OAAO;AAAA,QACP,MAAM,WAAY;AACd,cAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AACvB,iBAAO,EAAE,CAAC;AAAA,QACd;AAAA,QACA,MAAM,CAAC;AAAA,QACP,KAAK,CAAC;AAAA,MACV,GACA,GACA,GACA,GACA;AACJ,aACK,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,QAAQ,KAAK,CAAC,EAAE,GACtD,OAAO,WAAW,eACb,EAAE,OAAO,QAAQ,IAAI,WAAY;AAC9B,eAAO;AAAA,MACX,IACJ;AAEJ,eAAS,KAAK,GAAG;AACb,eAAO,SAAU,GAAG;AAChB,iBAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,QACtB;AAAA,MACJ;AACA,eAAS,KAAK,IAAI;AACd,YAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,eAAO;AACH,cAAI;AACA,gBACM,IAAI,GACN,MACK,IACG,GAAG,CAAC,IAAI,IACF,EAAE,QAAQ,IACV,GAAG,CAAC,IACJ,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAC/C,EAAE,SACZ,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAE5B,qBAAO;AACX,gBAAM,IAAI,GAAI,EAAI,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AAC1C,oBAAQ,GAAG,CAAC,GAAG;AAAA,cACX,KAAK;AAAA,cACL,KAAK;AACD,oBAAI;AACJ;AAAA,cACJ,KAAK;AACD,kBAAE;AACF,uBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,cACvC,KAAK;AACD,kBAAE;AACF,oBAAI,GAAG,CAAC;AACR,qBAAK,CAAC,CAAC;AACP;AAAA,cACJ,KAAK;AACD,qBAAK,EAAE,IAAI,IAAI;AACf,kBAAE,KAAK,IAAI;AACX;AAAA,cACJ;AACI,oBACI,EAAG,IAAI,EAAE,MAAQ,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAClD,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAC5B;AACE,sBAAI;AACJ;AAAA,gBACJ;AACA,oBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AACvD,oBAAE,QAAQ,GAAG,CAAC;AACd;AAAA,gBACJ;AACA,oBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAC/B,oBAAE,QAAQ,EAAE,CAAC;AACb,sBAAI;AACJ;AAAA,gBACJ;AACA,oBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AACrB,oBAAE,QAAQ,EAAE,CAAC;AACb,oBAAE,IAAI,KAAK,EAAE;AACb;AAAA,gBACJ;AACA,oBAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,kBAAE,KAAK,IAAI;AACX;AAAA,YACR;AACA,iBAAK,KAAK,KAAK,SAAS,CAAC;AAAA,UAC7B,SAAS,GAAG;AACR,iBAAK,CAAC,GAAG,CAAC;AACV,gBAAI;AAAA,UACR,UAAE;AACE,gBAAI,IAAI;AAAA,UACZ;AACJ,YAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AACzB,eAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,MACvD;AAAA,IACJ;AACJ,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,0BAA0B;AAClC,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,aAAS,wBAAwB,iBAAiB;AAC9C,UAAI,UAAU,IAAI,UAAU,QAAQ,gBAAgB,UAAU,gBAAgB,OAAO;AACrF,aAAO;AAAA,QACH,aAAa,WAAY;AACrB,cAAI,cAAc,GAAG,QAAQ,gBAAgB,UAAU;AACvD,eAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,KAAK,OAAO,IAAI;AACpF,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACX;AAAA,QACA,iBAAiB,SAAU,IAAI;AAC3B,cAAI,WAAW,GAAG,UACd,UAAU,GAAG,SACb,cAAc,GAAG;AACrB,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,gBAAI,aAAa,IAAI,UAAU,eAAe;AAC9C,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AACD,gCAAc,CAAC;AACf,sBAAI,gBAAgB,eAAe,QAAW;AAC1C,gCAAY,aAAa,gBAAgB;AAAA,kBAC7C;AACA,yBAAO;AAAA,oBACH;AAAA,oBACA,QAAQ;AAAA,sBACJ;AAAA,sBACA;AAAA,sBACA,CAAC;AAAA,sBACD;AAAA,sBACA,UAAU,QAAQ,kBAAkB;AAAA,wBAChC,kBAAkB,gBAAgB;AAAA,wBAClC,QAAQ;AAAA,wBACR;AAAA,wBACA;AAAA,sBACJ,CAAC;AAAA,sBACD,UAAU,QAAQ,mBAAmB;AAAA,wBACjC,mBAAmB,gBAAgB;AAAA,wBACnC,QAAQ;AAAA,wBACR;AAAA,sBACJ,CAAC;AAAA,oBACL;AAAA,kBACJ;AAAA,gBACJ,KAAK;AACD,kBAAC,KAAK,GAAG,KAAK,GAAK,WAAW,GAAG,UAAY,gBAAgB,GAAG;AAChE,sBAAI,SAAS,iBAAiB,QAAW;AACrC,mCAAe,CAAC;AAChB,wBAAI,SAAS,cAAc,SAAS;AAChC,mCAAa,KAAK,eAAe;AAAA,oBACrC;AACA,wBAAI,SAAS,WAAW,SAAS;AAC7B,mCAAa,KAAK,YAAY;AAAA,oBAClC;AACA,wBAAI,SAAS,aAAa,SAAS;AAC/B,mCAAa,KAAK,WAAW;AAC7B,mCAAa,KAAK,WAAW;AAC7B,mCAAa,KAAK,YAAY;AAC9B,mCAAa,KAAK,YAAY;AAAA,oBAClC;AAAA,kBACJ,OAAO;AACH,mCAAe,SAAS;AAAA,kBAC5B;AACA,yBAAO;AAAA,oBACH;AAAA,oBACA;AAAA,sBACI,QAAQ;AAAA,sBACR,YAAY;AAAA,wBACR,WAAW,SAAS,WAAW;AAAA,sBACnC;AAAA,sBACA;AAAA,sBACA;AAAA,oBACJ;AAAA,kBACJ;AAAA,cACR;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,UAAU;AAClB,YAAQ,0BAA0B;AAAA;AAAA;;;ACtOlC;AAAA;AAAA;AAeA,QAAI,YACC,WAAQ,QAAK,aACb,2BAAY;AACT,UAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,wBACI,OAAO,kBACN,EAAE,WAAW,CAAC,EAAE,aAAa,SAC1B,SAAUE,IAAGC,IAAG;AACZ,UAAAD,GAAE,YAAYC;AAAA,QAClB,KACJ,SAAUD,IAAGC,IAAG;AACZ,mBAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,QAC/E;AACJ,eAAO,cAAc,GAAG,CAAC;AAAA,MAC7B;AACA,aAAO,SAAU,GAAG,GAAG;AACnB,YAAI,OAAO,MAAM,cAAc,MAAM;AACjC,gBAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,sBAAc,GAAG,CAAC;AAClB,iBAAS,KAAK;AACV,eAAK,cAAc;AAAA,QACvB;AACA,UAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAM,GAAG,YAAY,EAAE,WAAY,IAAI,GAAG;AAAA,MACxF;AAAA,IACJ,EAAG;AACP,QAAI,YACC,WAAQ,QAAK,aACd,SAAU,SAAS,YAAY,GAAG,WAAW;AACzC,eAAS,MAAM,OAAO;AAClB,eAAO,iBAAiB,IAClB,QACA,IAAI,EAAE,SAAU,SAAS;AACrB,kBAAQ,KAAK;AAAA,QACjB,CAAC;AAAA,MACX;AACA,aAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,iBAAS,UAAU,OAAO;AACtB,cAAI;AACA,iBAAK,UAAU,KAAK,KAAK,CAAC;AAAA,UAC9B,SAAS,GAAG;AACR,mBAAO,CAAC;AAAA,UACZ;AAAA,QACJ;AACA,iBAAS,SAAS,OAAO;AACrB,cAAI;AACA,iBAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,UAClC,SAAS,GAAG;AACR,mBAAO,CAAC;AAAA,UACZ;AAAA,QACJ;AACA,iBAAS,KAAK,QAAQ;AAClB,iBAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,QACtF;AACA,cAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,MACxE,CAAC;AAAA,IACL;AACJ,QAAI,cACC,WAAQ,QAAK,eACd,SAAU,SAAS,MAAM;AACrB,UAAI,IAAI;AAAA,QACA,OAAO;AAAA,QACP,MAAM,WAAY;AACd,cAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AACvB,iBAAO,EAAE,CAAC;AAAA,QACd;AAAA,QACA,MAAM,CAAC;AAAA,QACP,KAAK,CAAC;AAAA,MACV,GACA,GACA,GACA,GACA;AACJ,aACK,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,QAAQ,KAAK,CAAC,EAAE,GACtD,OAAO,WAAW,eACb,EAAE,OAAO,QAAQ,IAAI,WAAY;AAC9B,eAAO;AAAA,MACX,IACJ;AAEJ,eAAS,KAAK,GAAG;AACb,eAAO,SAAU,GAAG;AAChB,iBAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,QACtB;AAAA,MACJ;AACA,eAAS,KAAK,IAAI;AACd,YAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,eAAO;AACH,cAAI;AACA,gBACM,IAAI,GACN,MACK,IACG,GAAG,CAAC,IAAI,IACF,EAAE,QAAQ,IACV,GAAG,CAAC,IACJ,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAC/C,EAAE,SACZ,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAE5B,qBAAO;AACX,gBAAM,IAAI,GAAI,EAAI,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AAC1C,oBAAQ,GAAG,CAAC,GAAG;AAAA,cACX,KAAK;AAAA,cACL,KAAK;AACD,oBAAI;AACJ;AAAA,cACJ,KAAK;AACD,kBAAE;AACF,uBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,cACvC,KAAK;AACD,kBAAE;AACF,oBAAI,GAAG,CAAC;AACR,qBAAK,CAAC,CAAC;AACP;AAAA,cACJ,KAAK;AACD,qBAAK,EAAE,IAAI,IAAI;AACf,kBAAE,KAAK,IAAI;AACX;AAAA,cACJ;AACI,oBACI,EAAG,IAAI,EAAE,MAAQ,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAClD,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAC5B;AACE,sBAAI;AACJ;AAAA,gBACJ;AACA,oBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AACvD,oBAAE,QAAQ,GAAG,CAAC;AACd;AAAA,gBACJ;AACA,oBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAC/B,oBAAE,QAAQ,EAAE,CAAC;AACb,sBAAI;AACJ;AAAA,gBACJ;AACA,oBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AACrB,oBAAE,QAAQ,EAAE,CAAC;AACb,oBAAE,IAAI,KAAK,EAAE;AACb;AAAA,gBACJ;AACA,oBAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,kBAAE,KAAK,IAAI;AACX;AAAA,YACR;AACA,iBAAK,KAAK,KAAK,SAAS,CAAC;AAAA,UAC7B,SAAS,GAAG;AACR,iBAAK,CAAC,GAAG,CAAC;AACV,gBAAI;AAAA,UACR,UAAE;AACE,gBAAI,IAAI;AAAA,UACZ;AACJ,YAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AACzB,eAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,MACvD;AAAA,IACJ;AACJ,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,QAAI,iBAAiB;AACrB,QAAI,WAAW;AACf,QAAI;AAAA;AAAA,MAA4B,SAAU,QAAQ;AAC9C,kBAAUC,aAAY,MAAM;AAC5B,iBAASA,YAAW,QAAQ;AACxB,cAAI,QAAQ,OAAO,KAAK,MAAM,MAAM,KAAK;AACzC,gBAAM,UAAU,SAAU,OAAO;AAC7B,mBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAChD,qBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,wBAAQ,GAAG,OAAO;AAAA,kBACd,KAAK;AACD,2BAAO;AAAA,sBACH;AAAA,sBACA,SAAS,QAAQ,mBAAmB,EAAE,QAAQ;AAAA,wBAC1C,aAAa,MAAM;AAAA,sBACvB,CAAC;AAAA,oBACL;AAAA,kBACJ,KAAK;AACD,2BAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,gBACvC;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UACL;AACA,iBAAO;AAAA,QACX;AACA,eAAOA;AAAA,MACX,EAAG,eAAe,OAAO;AAAA;AACzB,YAAQ,UAAU;AAAA;AAAA;;;ACvMlB,IAAAC,kBAAA;AAAA;AAAA;AAeA,QAAI,YACC,WAAQ,QAAK,aACb,2BAAY;AACT,UAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,wBACI,OAAO,kBACN,EAAE,WAAW,CAAC,EAAE,aAAa,SAC1B,SAAUC,IAAGC,IAAG;AACZ,UAAAD,GAAE,YAAYC;AAAA,QAClB,KACJ,SAAUD,IAAGC,IAAG;AACZ,mBAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,QAC/E;AACJ,eAAO,cAAc,GAAG,CAAC;AAAA,MAC7B;AACA,aAAO,SAAU,GAAG,GAAG;AACnB,YAAI,OAAO,MAAM,cAAc,MAAM;AACjC,gBAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,sBAAc,GAAG,CAAC;AAClB,iBAAS,KAAK;AACV,eAAK,cAAc;AAAA,QACvB;AACA,UAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAM,GAAG,YAAY,EAAE,WAAY,IAAI,GAAG;AAAA,MACxF;AAAA,IACJ,EAAG;AACP,QAAI,WACC,WAAQ,QAAK,YACd,WAAY;AACR,iBACI,OAAO,UACP,SAAU,GAAG;AACT,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACJ,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACJ,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,SAAS;AACjB,QAAI,UAAU;AACd,QAAI,4BAA4B;AAChC,QAAI,yBAAyB;AAC7B,QAAI,UAAU;AACd,QAAI,eAAe;AACnB,QAAI;AAAA;AAAA,MAAwB,SAAU,QAAQ;AAC1C,kBAAUC,SAAQ,MAAM;AACxB,iBAASA,QAAO,QAAQ;AACpB,cAAI,QAAQ,OAAO,KAAK,OAAO,GAAG,QAAQ,oBAAoB,MAAM,CAAC,KAAK;AAC1E,cAAI,UAAU,IAAI,0BAA0B;AAAA,aACvC,GAAG,uBAAuB,SAAS;AAAA,cAChC,UAAU,MAAM,OAAO;AAAA,cACvB,SAAS,MAAM,OAAO;AAAA,cACtB,YAAY,MAAM,OAAO;AAAA,cACzB,YAAY,MAAM,OAAO;AAAA,cACzB,aAAa,MAAM,OAAO;AAAA,YAC9B,CAAC;AAAA,UACL;AACA,gBAAM,uBAAuB,QAAQ,SAAS,MAAM,OAAO,SAAS,SAAS,EAAE,MAAM;AACrF,iBAAO;AAAA,QACX;AACA,QAAAA,QAAO,OAAO,SAAU,QAAQ;AAC5B,iBAAO,SAAU,SAAS,YAAY;AAClC,YAAAA,QAAO,WAAW,IAAIA;AAAA,cAClB,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,UAAUA,QAAO,WAAW,SAAkB,WAAuB,CAAC;AAAA,YAC3G;AACA,mBAAOA,QAAO;AAAA,UAClB;AAAA,QACJ;AACA,QAAAA,QAAO,qBAAqB,WAAY;AACpC,cAAIA,QAAO,aAAa,QAAW;AAC/B,gBAAI,QAAQ;AACZ,qBAAS,GAAG,QAAQ,mCAAmC,KAAK;AAC5D,kBAAM,MAAM,KAAK;AAAA,UACrB;AACA,iBAAOA,QAAO;AAAA,QAClB;AACA,QAAAA,QAAO,QAAQ,WAAY;AACvB,cAAI,EAAE,GAAG,QAAQ,QAAQ,GAAG;AACxB;AAAA,UACJ;AACA,UAAAA,QAAO,WAAW;AAClB;AAAA,QACJ;AACA,QAAAA,QAAO,YAAY;AACnB,eAAOA;AAAA,MACX,EAAG,aAAa,OAAO;AAAA;AACvB,YAAQ,SAAS;AACjB,YAAQ,UAAU;AAAA;AAAA;;;ACxGlB,IAAAC,wBAAA;AAAA;AAAA;AAeA,aAAS,SAAS,GAAG;AACjB,eAAS,KAAK,EAAG,KAAI,CAAC,QAAQ,eAAe,CAAC,EAAG,SAAQ,CAAC,IAAI,EAAE,CAAC;AAAA,IACrE;AACA,YAAQ,aAAa;AACrB,aAAS,sBAA6C;AAAA;AAAA;;;ACnBtD,IAAAC,wBAAA;AAAA;AAAA;AAeA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,wBAAwB;AAChC,QAAI,iBAAiB;AACrB,WAAO,eAAe,SAAS,yBAAyB;AAAA,MACpD,YAAY;AAAA,MACZ,KAAK,WAAY;AACb,eAAO,eAAe;AAAA,MAC1B;AAAA,IACJ,CAAC;AAAA;AAAA;;;ACvBD;AAAA;AAAA;AAeA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,QAAI,UAAU;AACd,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AACtB,QAAI,iCAAiC;AACrC,QAAI,WAAW;AACf,QAAI,iBAAiB;AACrB,QAAI;AAAA;AAAA,MAA6B,WAAY;AACzC,iBAASC,aAAY,QAAQ;AACzB,cAAI,QAAQ;AACZ,eAAK,aAAa,CAAC;AACnB,eAAK,WAAW,GAAG,QAAQ,mCAAmC,OAAO,OAAO;AAC5E,cAAI,OAAO,eAAe,UAAa,OAAO,WAAW,WAAW,GAAG;AACnE,kBAAM,IAAI;AAAA,cACN;AAAA,YACJ;AAAA,UACJ;AACA,cAAI,kBAAkB;AACtB,cAAI,OAAO,oBAAoB,QAAW;AACtC,8BAAkB,OAAO;AAAA,UAC7B;AACA,cAAI,oBAAoB;AACxB,eAAK,aAAa,OAAO,WAAW,IAAI,SAAU,QAAQ;AACtD,gBAAI,iBAAiB,OAAO,MAAM,SAAS,OAAO,YAAY,eAAe;AAC7E,gBAAI,eAAe,OAAO,aAAa,SAAS,OAAO,WAAW;AAC9D,kCAAoB;AAAA,YACxB;AACA,mBAAO;AAAA,UACX,CAAC;AACD,cAAI,CAAC,mBAAmB;AACpB,iBAAK,WAAW,KAAK,SAAS,OAAO,KAAK,EAAE,KAAK,SAAS,OAAO,YAAY,eAAe,CAAC;AAAA,UACjG;AAAA,QACJ;AAOA,QAAAA,aAAY,OAAO,SAAU,QAAQ;AACjC,0BAAgB,uBAAuB,KAAK,OAAO,aAAa;AAChE,0BAAgB,uBAAuB,KAAK,OAAO,aAAa;AAChE,yBAAe,sBAAsB,KAAK,OAAO,YAAY;AAC7D,cAAIA,aAAY,aAAa,QAAW;AACpC,oBAAQ,KAAK,qCAAqC;AAClD;AAAA,UACJ;AACA,UAAAA,aAAY,WAAW,IAAIA,aAAY,MAAM;AAC7C,yCAA+B,6BAA6B,qBAAqB;AAAA,QACrF;AAQA,QAAAA,aAAY,qBAAqB,WAAY;AACzC,cAAIA,aAAY,aAAa,QAAW;AACpC,gBAAI,QAAQ;AACZ,qBAAS,GAAG,QAAQ,mCAAmC,KAAK;AAC5D,kBAAM,IAAI,MAAM,KAAK;AAAA,UACzB;AACA,iBAAOA,aAAY;AAAA,QACvB;AACA,QAAAA,aAAY,QAAQ,WAAY;AAC5B,cAAI,EAAE,GAAG,QAAQ,QAAQ,GAAG;AACxB,oBAAQ,KAAK,kDAAkD;AAC/D;AAAA,UACJ;AAGA,mBAAS,OAAO,MAAM;AACtB,UAAAA,aAAY,WAAW;AACvB;AAAA,QACJ;AACA,eAAOA;AAAA,MACX,EAAG;AAAA;AACH,YAAQ,UAAU;AAAA;AAAA;;;AC7FlB,IAAAC,iBAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,OAAO;AAef,QAAI,gBAAgB;AACpB,QAAI;AAAA;AAAA,MAAuC,WAAY;AACnD,iBAASC,yBAAwB;AAAA,QAAC;AAClC,QAAAA,uBAAsB,OAAO,SAAU,QAAQ;AAC3C,wBAAc,QAAQ,KAAK,MAAM;AAAA,QACrC;AACA,eAAOA;AAAA,MACX,EAAG;AAAA;AACH,YAAQ,UAAU;AAClB,YAAQ,OAAO,sBAAsB;AAAA;AAAA;;;AC1BrC;AAAA;AAgBA,aAAS,SAAS,GAAG;AACjB,eAAS,KAAK,EAAG,KAAI,CAAC,QAAQ,eAAe,CAAC,EAAG,SAAQ,CAAC,IAAI,EAAE,CAAC;AAAA,IACrE;AACA,YAAQ,aAAa;AACrB,aAAS,gBAAuB;AAAA;AAAA;", "names": ["require_cookieHandler", "require_cookieHandler", "PostSuperTokensInitCallbacks", "require_utils", "require_utils", "require_utils", "require_error", "require_error", "<PERSON><PERSON>", "_b", "d", "b", "AuthRecipe", "require_recipe", "d", "b", "Recipe", "require_dateProvider", "require_dateProvider", "SuperTokens", "require_build", "SuperTokensAPIWrapper"]}
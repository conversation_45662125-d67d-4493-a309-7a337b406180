{"version": 3, "sources": ["../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/lib/build/recipe/session/index.js", "../../.pnpm/supertokens-web-js@0.15.0/node_modules/supertokens-web-js/recipe/session/index.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter =\n    (this && this.__awaiter) ||\n    function (thisArg, _arguments, P, generator) {\n        function adopt(value) {\n            return value instanceof P\n                ? value\n                : new P(function (resolve) {\n                      resolve(value);\n                  });\n        }\n        return new (P || (P = Promise))(function (resolve, reject) {\n            function fulfilled(value) {\n                try {\n                    step(generator.next(value));\n                } catch (e) {\n                    reject(e);\n                }\n            }\n            function rejected(value) {\n                try {\n                    step(generator[\"throw\"](value));\n                } catch (e) {\n                    reject(e);\n                }\n            }\n            function step(result) {\n                result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n            }\n            step((generator = generator.apply(thisArg, _arguments || [])).next());\n        });\n    };\nvar __generator =\n    (this && this.__generator) ||\n    function (thisArg, body) {\n        var _ = {\n                label: 0,\n                sent: function () {\n                    if (t[0] & 1) throw t[1];\n                    return t[1];\n                },\n                trys: [],\n                ops: [],\n            },\n            f,\n            y,\n            t,\n            g;\n        return (\n            (g = { next: verb(0), throw: verb(1), return: verb(2) }),\n            typeof Symbol === \"function\" &&\n                (g[Symbol.iterator] = function () {\n                    return this;\n                }),\n            g\n        );\n        function verb(n) {\n            return function (v) {\n                return step([n, v]);\n            };\n        }\n        function step(op) {\n            if (f) throw new TypeError(\"Generator is already executing.\");\n            while (_)\n                try {\n                    if (\n                        ((f = 1),\n                        y &&\n                            (t =\n                                op[0] & 2\n                                    ? y[\"return\"]\n                                    : op[0]\n                                    ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0)\n                                    : y.next) &&\n                            !(t = t.call(y, op[1])).done)\n                    )\n                        return t;\n                    if (((y = 0), t)) op = [op[0] & 2, t.value];\n                    switch (op[0]) {\n                        case 0:\n                        case 1:\n                            t = op;\n                            break;\n                        case 4:\n                            _.label++;\n                            return { value: op[1], done: false };\n                        case 5:\n                            _.label++;\n                            y = op[1];\n                            op = [0];\n                            continue;\n                        case 7:\n                            op = _.ops.pop();\n                            _.trys.pop();\n                            continue;\n                        default:\n                            if (\n                                !((t = _.trys), (t = t.length > 0 && t[t.length - 1])) &&\n                                (op[0] === 6 || op[0] === 2)\n                            ) {\n                                _ = 0;\n                                continue;\n                            }\n                            if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) {\n                                _.label = op[1];\n                                break;\n                            }\n                            if (op[0] === 6 && _.label < t[1]) {\n                                _.label = t[1];\n                                t = op;\n                                break;\n                            }\n                            if (t && _.label < t[2]) {\n                                _.label = t[2];\n                                _.ops.push(op);\n                                break;\n                            }\n                            if (t[2]) _.ops.pop();\n                            _.trys.pop();\n                            continue;\n                    }\n                    op = body.call(thisArg, _);\n                } catch (e) {\n                    op = [6, e];\n                    y = 0;\n                } finally {\n                    f = t = 0;\n                }\n            if (op[0] & 5) throw op[1];\n            return { value: op[0] ? op[1] : void 0, done: true };\n        }\n    };\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getInvalidClaimsFromResponse =\n    exports.getClaimValue =\n    exports.validateClaims =\n    exports.signOut =\n    exports.addAxiosInterceptors =\n    exports.doesSessionExist =\n    exports.attemptRefreshingSession =\n    exports.getAccessToken =\n    exports.getAccessTokenPayloadSecurely =\n    exports.getUserId =\n    exports.init =\n    exports.BooleanClaim =\n    exports.PrimitiveArrayClaim =\n    exports.PrimitiveClaim =\n        void 0;\n/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\nvar utils_1 = require(\"../../utils\");\nvar recipe_1 = require(\"./recipe\");\nvar RecipeWrapper = /** @class */ (function () {\n    function RecipeWrapper() {}\n    RecipeWrapper.init = function (config) {\n        return recipe_1.default.init(config);\n    };\n    RecipeWrapper.getUserId = function (input) {\n        return recipe_1.default.getInstanceOrThrow().getUserId({\n            userContext: (0, utils_1.getNormalisedUserContext)(\n                input === null || input === void 0 ? void 0 : input.userContext\n            ),\n        });\n    };\n    RecipeWrapper.getAccessToken = function (input) {\n        return recipe_1.default.getInstanceOrThrow().getAccessToken({\n            userContext: (0, utils_1.getNormalisedUserContext)(\n                input === null || input === void 0 ? void 0 : input.userContext\n            ),\n        });\n    };\n    RecipeWrapper.getAccessTokenPayloadSecurely = function (input) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                return [\n                    2 /*return*/,\n                    recipe_1.default.getInstanceOrThrow().getAccessTokenPayloadSecurely({\n                        userContext: (0, utils_1.getNormalisedUserContext)(\n                            input === null || input === void 0 ? void 0 : input.userContext\n                        ),\n                    }),\n                ];\n            });\n        });\n    };\n    RecipeWrapper.attemptRefreshingSession = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                return [2 /*return*/, recipe_1.default.getInstanceOrThrow().attemptRefreshingSession()];\n            });\n        });\n    };\n    RecipeWrapper.doesSessionExist = function (input) {\n        return recipe_1.default.getInstanceOrThrow().doesSessionExist({\n            userContext: (0, utils_1.getNormalisedUserContext)(\n                input === null || input === void 0 ? void 0 : input.userContext\n            ),\n        });\n    };\n    /**\n     * @deprecated\n     */\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n    RecipeWrapper.addAxiosInterceptors = function (axiosInstance, userContext) {\n        return recipe_1.default.addAxiosInterceptors(axiosInstance, (0, utils_1.getNormalisedUserContext)(userContext));\n    };\n    RecipeWrapper.signOut = function (input) {\n        return recipe_1.default.getInstanceOrThrow().signOut({\n            userContext: (0, utils_1.getNormalisedUserContext)(\n                input === null || input === void 0 ? void 0 : input.userContext\n            ),\n        });\n    };\n    RecipeWrapper.getClaimValue = function (input) {\n        return recipe_1.default.getInstanceOrThrow().getClaimValue({\n            claim: input.claim,\n            userContext: (0, utils_1.getNormalisedUserContext)(\n                input === null || input === void 0 ? void 0 : input.userContext\n            ),\n        });\n    };\n    RecipeWrapper.validateClaims = function (input) {\n        return recipe_1.default.getInstanceOrThrow().validateClaims({\n            overrideGlobalClaimValidators:\n                input === null || input === void 0 ? void 0 : input.overrideGlobalClaimValidators,\n            userContext: (0, utils_1.getNormalisedUserContext)(\n                input === null || input === void 0 ? void 0 : input.userContext\n            ),\n        });\n    };\n    // The strange typing is to avoid adding a dependency to axios\n    RecipeWrapper.getInvalidClaimsFromResponse = function (input) {\n        return recipe_1.default.getInstanceOrThrow().getInvalidClaimsFromResponse({\n            response: input.response,\n            userContext: (0, utils_1.getNormalisedUserContext)(\n                input === null || input === void 0 ? void 0 : input.userContext\n            ),\n        });\n    };\n    return RecipeWrapper;\n})();\nexports.default = RecipeWrapper;\nvar init = RecipeWrapper.init;\nexports.init = init;\nvar getUserId = RecipeWrapper.getUserId;\nexports.getUserId = getUserId;\nvar getAccessTokenPayloadSecurely = RecipeWrapper.getAccessTokenPayloadSecurely;\nexports.getAccessTokenPayloadSecurely = getAccessTokenPayloadSecurely;\nvar getAccessToken = RecipeWrapper.getAccessToken;\nexports.getAccessToken = getAccessToken;\nvar attemptRefreshingSession = RecipeWrapper.attemptRefreshingSession;\nexports.attemptRefreshingSession = attemptRefreshingSession;\nvar doesSessionExist = RecipeWrapper.doesSessionExist;\nexports.doesSessionExist = doesSessionExist;\n/**\n * @deprecated\n */\nvar addAxiosInterceptors = RecipeWrapper.addAxiosInterceptors;\nexports.addAxiosInterceptors = addAxiosInterceptors;\nvar signOut = RecipeWrapper.signOut;\nexports.signOut = signOut;\nvar validateClaims = RecipeWrapper.validateClaims;\nexports.validateClaims = validateClaims;\nvar getClaimValue = RecipeWrapper.getClaimValue;\nexports.getClaimValue = getClaimValue;\nvar getInvalidClaimsFromResponse = RecipeWrapper.getInvalidClaimsFromResponse;\nexports.getInvalidClaimsFromResponse = getInvalidClaimsFromResponse;\nvar supertokens_website_1 = require(\"supertokens-website\");\nObject.defineProperty(exports, \"PrimitiveClaim\", {\n    enumerable: true,\n    get: function () {\n        return supertokens_website_1.PrimitiveClaim;\n    },\n});\nObject.defineProperty(exports, \"PrimitiveArrayClaim\", {\n    enumerable: true,\n    get: function () {\n        return supertokens_website_1.PrimitiveArrayClaim;\n    },\n});\nObject.defineProperty(exports, \"BooleanClaim\", {\n    enumerable: true,\n    get: function () {\n        return supertokens_website_1.BooleanClaim;\n    },\n});\n", "/* Copyright (c) 2022, VRAI Labs and/or its affiliates. All rights reserved.\n *\n * This software is licensed under the Apache License, Version 2.0 (the\n * \"License\") as published by the Apache Software Foundation.\n *\n * You may not use this file except in compliance with the License. You may\n * obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations\n * under the License.\n */\n\"use strict\";\nfunction __export(m) {\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\n}\nexports.__esModule = true;\n__export(require(\"../../lib/build/recipe/session\"));\n"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AACA,QAAI,YACC,WAAQ,QAAK,aACd,SAAU,SAAS,YAAY,GAAG,WAAW;AACzC,eAAS,MAAM,OAAO;AAClB,eAAO,iBAAiB,IAClB,QACA,IAAI,EAAE,SAAU,SAAS;AACrB,kBAAQ,KAAK;AAAA,QACjB,CAAC;AAAA,MACX;AACA,aAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,iBAAS,UAAU,OAAO;AACtB,cAAI;AACA,iBAAK,UAAU,KAAK,KAAK,CAAC;AAAA,UAC9B,SAAS,GAAG;AACR,mBAAO,CAAC;AAAA,UACZ;AAAA,QACJ;AACA,iBAAS,SAAS,OAAO;AACrB,cAAI;AACA,iBAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,UAClC,SAAS,GAAG;AACR,mBAAO,CAAC;AAAA,UACZ;AAAA,QACJ;AACA,iBAAS,KAAK,QAAQ;AAClB,iBAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,QACtF;AACA,cAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,MACxE,CAAC;AAAA,IACL;AACJ,QAAI,cACC,WAAQ,QAAK,eACd,SAAU,SAAS,MAAM;AACrB,UAAI,IAAI;AAAA,QACA,OAAO;AAAA,QACP,MAAM,WAAY;AACd,cAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AACvB,iBAAO,EAAE,CAAC;AAAA,QACd;AAAA,QACA,MAAM,CAAC;AAAA,QACP,KAAK,CAAC;AAAA,MACV,GACA,GACA,GACA,GACA;AACJ,aACK,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG,QAAQ,KAAK,CAAC,EAAE,GACtD,OAAO,WAAW,eACb,EAAE,OAAO,QAAQ,IAAI,WAAY;AAC9B,eAAO;AAAA,MACX,IACJ;AAEJ,eAAS,KAAK,GAAG;AACb,eAAO,SAAU,GAAG;AAChB,iBAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,QACtB;AAAA,MACJ;AACA,eAAS,KAAK,IAAI;AACd,YAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,eAAO;AACH,cAAI;AACA,gBACM,IAAI,GACN,MACK,IACG,GAAG,CAAC,IAAI,IACF,EAAE,QAAQ,IACV,GAAG,CAAC,IACJ,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAC/C,EAAE,SACZ,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAE5B,qBAAO;AACX,gBAAM,IAAI,GAAI,EAAI,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AAC1C,oBAAQ,GAAG,CAAC,GAAG;AAAA,cACX,KAAK;AAAA,cACL,KAAK;AACD,oBAAI;AACJ;AAAA,cACJ,KAAK;AACD,kBAAE;AACF,uBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,cACvC,KAAK;AACD,kBAAE;AACF,oBAAI,GAAG,CAAC;AACR,qBAAK,CAAC,CAAC;AACP;AAAA,cACJ,KAAK;AACD,qBAAK,EAAE,IAAI,IAAI;AACf,kBAAE,KAAK,IAAI;AACX;AAAA,cACJ;AACI,oBACI,EAAG,IAAI,EAAE,MAAQ,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAClD,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAC5B;AACE,sBAAI;AACJ;AAAA,gBACJ;AACA,oBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AACvD,oBAAE,QAAQ,GAAG,CAAC;AACd;AAAA,gBACJ;AACA,oBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAC/B,oBAAE,QAAQ,EAAE,CAAC;AACb,sBAAI;AACJ;AAAA,gBACJ;AACA,oBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AACrB,oBAAE,QAAQ,EAAE,CAAC;AACb,oBAAE,IAAI,KAAK,EAAE;AACb;AAAA,gBACJ;AACA,oBAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,kBAAE,KAAK,IAAI;AACX;AAAA,YACR;AACA,iBAAK,KAAK,KAAK,SAAS,CAAC;AAAA,UAC7B,SAAS,GAAG;AACR,iBAAK,CAAC,GAAG,CAAC;AACV,gBAAI;AAAA,UACR,UAAE;AACE,gBAAI,IAAI;AAAA,UACZ;AACJ,YAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AACzB,eAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,MACvD;AAAA,IACJ;AACJ,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,+BACJ,QAAQ,gBACR,QAAQ,iBACR,QAAQ,UACR,QAAQ,uBACR,QAAQ,mBACR,QAAQ,2BACR,QAAQ,iBACR,QAAQ,gCACR,QAAQ,YACR,QAAQ,OACR,QAAQ,eACR,QAAQ,sBACR,QAAQ,iBACJ;AAeR,QAAI,UAAU;AACd,QAAI,WAAW;AACf,QAAI;AAAA;AAAA,MAA+B,WAAY;AAC3C,iBAASA,iBAAgB;AAAA,QAAC;AAC1B,QAAAA,eAAc,OAAO,SAAU,QAAQ;AACnC,iBAAO,SAAS,QAAQ,KAAK,MAAM;AAAA,QACvC;AACA,QAAAA,eAAc,YAAY,SAAU,OAAO;AACvC,iBAAO,SAAS,QAAQ,mBAAmB,EAAE,UAAU;AAAA,YACnD,cAAc,GAAG,QAAQ;AAAA,cACrB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AAAA,YACxD;AAAA,UACJ,CAAC;AAAA,QACL;AACA,QAAAA,eAAc,iBAAiB,SAAU,OAAO;AAC5C,iBAAO,SAAS,QAAQ,mBAAmB,EAAE,eAAe;AAAA,YACxD,cAAc,GAAG,QAAQ;AAAA,cACrB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AAAA,YACxD;AAAA,UACJ,CAAC;AAAA,QACL;AACA,QAAAA,eAAc,gCAAgC,SAAU,OAAO;AAC3D,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,qBAAO;AAAA,gBACH;AAAA,gBACA,SAAS,QAAQ,mBAAmB,EAAE,8BAA8B;AAAA,kBAChE,cAAc,GAAG,QAAQ;AAAA,oBACrB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AAAA,kBACxD;AAAA,gBACJ,CAAC;AAAA,cACL;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAA,eAAc,2BAA2B,WAAY;AACjD,iBAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,qBAAO,CAAC,GAAc,SAAS,QAAQ,mBAAmB,EAAE,yBAAyB,CAAC;AAAA,YAC1F,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,QAAAA,eAAc,mBAAmB,SAAU,OAAO;AAC9C,iBAAO,SAAS,QAAQ,mBAAmB,EAAE,iBAAiB;AAAA,YAC1D,cAAc,GAAG,QAAQ;AAAA,cACrB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AAAA,YACxD;AAAA,UACJ,CAAC;AAAA,QACL;AAKA,QAAAA,eAAc,uBAAuB,SAAU,eAAe,aAAa;AACvE,iBAAO,SAAS,QAAQ,qBAAqB,gBAAgB,GAAG,QAAQ,0BAA0B,WAAW,CAAC;AAAA,QAClH;AACA,QAAAA,eAAc,UAAU,SAAU,OAAO;AACrC,iBAAO,SAAS,QAAQ,mBAAmB,EAAE,QAAQ;AAAA,YACjD,cAAc,GAAG,QAAQ;AAAA,cACrB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AAAA,YACxD;AAAA,UACJ,CAAC;AAAA,QACL;AACA,QAAAA,eAAc,gBAAgB,SAAU,OAAO;AAC3C,iBAAO,SAAS,QAAQ,mBAAmB,EAAE,cAAc;AAAA,YACvD,OAAO,MAAM;AAAA,YACb,cAAc,GAAG,QAAQ;AAAA,cACrB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AAAA,YACxD;AAAA,UACJ,CAAC;AAAA,QACL;AACA,QAAAA,eAAc,iBAAiB,SAAU,OAAO;AAC5C,iBAAO,SAAS,QAAQ,mBAAmB,EAAE,eAAe;AAAA,YACxD,+BACI,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AAAA,YACxD,cAAc,GAAG,QAAQ;AAAA,cACrB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AAAA,YACxD;AAAA,UACJ,CAAC;AAAA,QACL;AAEA,QAAAA,eAAc,+BAA+B,SAAU,OAAO;AAC1D,iBAAO,SAAS,QAAQ,mBAAmB,EAAE,6BAA6B;AAAA,YACtE,UAAU,MAAM;AAAA,YAChB,cAAc,GAAG,QAAQ;AAAA,cACrB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AAAA,YACxD;AAAA,UACJ,CAAC;AAAA,QACL;AACA,eAAOA;AAAA,MACX,EAAG;AAAA;AACH,YAAQ,UAAU;AAClB,QAAI,OAAO,cAAc;AACzB,YAAQ,OAAO;AACf,QAAI,YAAY,cAAc;AAC9B,YAAQ,YAAY;AACpB,QAAI,gCAAgC,cAAc;AAClD,YAAQ,gCAAgC;AACxC,QAAI,iBAAiB,cAAc;AACnC,YAAQ,iBAAiB;AACzB,QAAI,2BAA2B,cAAc;AAC7C,YAAQ,2BAA2B;AACnC,QAAI,mBAAmB,cAAc;AACrC,YAAQ,mBAAmB;AAI3B,QAAI,uBAAuB,cAAc;AACzC,YAAQ,uBAAuB;AAC/B,QAAI,UAAU,cAAc;AAC5B,YAAQ,UAAU;AAClB,QAAI,iBAAiB,cAAc;AACnC,YAAQ,iBAAiB;AACzB,QAAI,gBAAgB,cAAc;AAClC,YAAQ,gBAAgB;AACxB,QAAI,+BAA+B,cAAc;AACjD,YAAQ,+BAA+B;AACvC,QAAI,wBAAwB;AAC5B,WAAO,eAAe,SAAS,kBAAkB;AAAA,MAC7C,YAAY;AAAA,MACZ,KAAK,WAAY;AACb,eAAO,sBAAsB;AAAA,MACjC;AAAA,IACJ,CAAC;AACD,WAAO,eAAe,SAAS,uBAAuB;AAAA,MAClD,YAAY;AAAA,MACZ,KAAK,WAAY;AACb,eAAO,sBAAsB;AAAA,MACjC;AAAA,IACJ,CAAC;AACD,WAAO,eAAe,SAAS,gBAAgB;AAAA,MAC3C,YAAY;AAAA,MACZ,KAAK,WAAY;AACb,eAAO,sBAAsB;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA;AAAA;;;ACzSD,IAAAC,mBAAA;AAAA;AAeA,aAAS,SAAS,GAAG;AACjB,eAAS,KAAK,EAAG,KAAI,CAAC,QAAQ,eAAe,CAAC,EAAG,SAAQ,CAAC,IAAI,EAAE,CAAC;AAAA,IACrE;AACA,YAAQ,aAAa;AACrB,aAAS,iBAAyC;AAAA;AAAA;", "names": ["RecipeWrapper", "require_session"]}
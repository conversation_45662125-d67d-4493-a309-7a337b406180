{"name": "vue", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "preview": "vite preview"}, "dependencies": {"@mdi/font": "^7.4.47", "browser-tabs-lock": "^1.3.0", "supertokens-web-js": "latest", "vue": "^3.5.17", "vue-router": "^4.5.1", "vuetify": "^3.9.0"}, "devDependencies": {"@types/node": "^22.16.3", "@vitejs/plugin-vue": "^5.2.4", "esbuild": "0.25.5", "typescript": "~5.6.3", "vite": "^6.3.5", "vue-tsc": "^2.2.12"}}
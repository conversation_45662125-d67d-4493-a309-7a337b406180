<template>
  <div class="profile-form">
    <v-form ref="form" v-model="valid" @submit.prevent="onSubmit" class="modern-form">
      <!-- Nom et Prénom -->
      <div class="form-section">
        <h3 class="section-title">Informations personnelles</h3>
        <v-row class="form-row">
          <v-col cols="12" md="6">
            <div class="field-container">
              <label class="field-label">
                Prénom <span class="required-asterisk">*</span>
              </label>
              <v-text-field
                v-model="formData.first_name"
                :rules="nameRules"
                variant="solo-filled"
                density="comfortable"
                hide-details="auto"
                class="modern-field"
                placeholder="Votre prénom"
              />
            </div>
          </v-col>
          <v-col cols="12" md="6">
            <div class="field-container">
              <label class="field-label">
                Nom <span class="required-asterisk">*</span>
              </label>
              <v-text-field
                v-model="formData.last_name"
                :rules="nameRules"
                variant="solo-filled"
                density="comfortable"
                hide-details="auto"
                class="modern-field"
                placeholder="Votre nom de famille"
              />
            </div>
          </v-col>
        </v-row>
        
        <div class="field-container">
          <label class="field-label">Nom de famille (deuxième prénom)</label>
          <v-text-field
            v-model="formData.middle_name"
            variant="solo-filled"
            density="comfortable"
            hide-details="auto"
            class="modern-field"
            placeholder="Votre deuxième prénom"
          />
        </div>
      </div>

      <!-- Contact et Bio -->
      <div class="form-section">
        <h3 class="section-title">Informations de contact</h3>
        
        <div class="field-container">
          <label class="field-label">
            <v-icon size="small" class="mr-1">mdi-phone</v-icon>
            Numéro de téléphone
          </label>
          <v-text-field
            v-model="formData.phone_number"
            :rules="phoneRules"
            variant="solo-filled"
            density="comfortable"
            hide-details="auto"
            class="modern-field"
            placeholder="+33 6 12 34 56 78"
          />
        </div>
        
        <div class="field-container">
          <label class="field-label">
            <v-icon size="small" class="mr-1">mdi-text-account</v-icon>
            À propos de vous
          </label>
          <v-textarea
            v-model="formData.bio"
            variant="solo-filled"
            density="comfortable"
            hide-details="auto"
            rows="4"
            class="modern-field"
            placeholder="Parlez-nous un peu de vous..."
            auto-grow
            counter="500"
            maxlength="500"
          />
        </div>
        
        <div class="field-container">
          <label class="field-label">
            <v-icon size="small" class="mr-1">mdi-image-outline</v-icon>
            Photo de profil
          </label>
          <v-text-field
            v-model="formData.avatar_url"
            variant="solo-filled"
            density="comfortable"
            hide-details="auto"
            class="modern-field"
            placeholder="https://exemple.com/votre-photo.jpg"
          />
        </div>
      </div>
      
      <!-- Boutons d'action -->
      <div class="action-section">
        <v-row class="action-row">
          <v-col cols="12" :sm="isUpdate ? 12 : 6">
            <v-btn
              type="submit"
              :loading="loading"
              :disabled="!valid"
              class="action-btn primary-btn"
              size="large"
              block
              elevation="2"
            >
              <v-icon left>{{ isUpdate ? 'mdi-account-edit' : 'mdi-check-circle' }}</v-icon>
              {{ isUpdate ? 'Mettre à jour le profil' : 'Compléter le profil' }}
            </v-btn>
          </v-col>
          <v-col cols="12" sm="6" v-if="!isUpdate">
            <v-btn
              :loading="loading"
              @click="onSkip"
              class="action-btn secondary-btn"
              variant="outlined"
              size="large"
              block
            >
              <v-icon left>mdi-skip-next</v-icon>
              Passer pour l'instant
            </v-btn>
          </v-col>
        </v-row>
      </div>
    </v-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'

interface Props {
  initialData?: any
  isUpdate?: boolean
  loading?: boolean
}

interface Emits {
  (e: 'submit', data: any): void
  (e: 'skip'): void
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
  isUpdate: false,
  loading: false
})

const emit = defineEmits<Emits>()

const form = ref()
const valid = ref(false)

const formData = reactive({
  first_name: '',
  last_name: '',
  middle_name: '',
  phone_number: '',
  bio: '',
  avatar_url: ''
})

const nameRules = [
  (v: string) => !!v || 'Ce champ est obligatoire',
  (v: string) => (v && v.length >= 2) || 'Doit contenir au moins 2 caractères',
  (v: string) => (v && v.length <= 50) || 'Ne doit pas dépasser 50 caractères'
]

const phoneRules = [
  (v: string) => !v || /^[\+]?[1-9][\d]{0,15}$/.test(v) || 'Format de téléphone invalide (ex: +33123456789)'
]

// Watch for initial data changes
watch(() => props.initialData, (newData) => {
  if (newData) {
    Object.assign(formData, newData)
  }
}, { immediate: true })

function onSubmit() {
  if (valid.value) {
    // Nettoyer les données avant envoi
    const cleanData = Object.fromEntries(
      Object.entries(formData).map(([key, value]) => [
        key, 
        typeof value === 'string' ? value.trim() : value
      ])
    )
    emit('submit', cleanData)
  }
}

function onSkip() {
  emit('skip')
}
</script>

<style scoped>
.profile-form {
  max-width: 100%;
  margin: 0 auto;
}

.modern-form {
  padding: 0;
}

.form-section {
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: rgb(var(--v-theme-primary));
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, rgb(var(--v-theme-primary)), rgb(var(--v-theme-secondary)));
  border-radius: 2px;
}

.field-container {
  margin-bottom: 1.5rem;
}

.field-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: rgba(var(--v-theme-on-surface), 0.87);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.required-asterisk {
  color: rgb(var(--v-theme-error));
  font-weight: bold;
  font-size: 1rem;
}

.form-row {
  margin-bottom: 0;
}

.modern-field {
  border-radius: 12px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-field :deep(.v-field) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-field :deep(.v-field--focused) {
  box-shadow: 0 4px 20px rgba(var(--v-theme-primary), 0.2);
  transform: translateY(-1px);
}

.modern-field :deep(.v-field__input) {
  padding: 1rem 1rem;
  font-size: 1rem;
}

.modern-field :deep(.v-field__field) {
  border-radius: 12px;
}

.modern-field :deep(.v-field__overlay) {
  border-radius: 12px;
}

.action-section {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(var(--v-theme-on-surface), 0.12);
}

.action-row {
  gap: 1rem;
}

.action-btn {
  border-radius: 12px !important;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.025em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 56px !important;
}

.primary-btn {
  background: linear-gradient(135deg, rgb(var(--v-theme-primary)), rgb(var(--v-theme-secondary))) !important;
  color: white !important;
  box-shadow: 0 4px 16px rgba(var(--v-theme-primary), 0.3);
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--v-theme-primary), 0.4);
}

.primary-btn:active {
  transform: translateY(0);
}

.secondary-btn {
  border: 2px solid rgb(var(--v-theme-primary)) !important;
  color: rgb(var(--v-theme-primary)) !important;
}

.secondary-btn:hover {
  background: rgba(var(--v-theme-primary), 0.05) !important;
  transform: translateY(-1px);
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .form-section {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .section-title {
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }
  
  .field-container {
    margin-bottom: 1rem;
  }
  
  .action-section {
    margin-top: 1.5rem;
    padding-top: 1rem;
  }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .form-section {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.15);
  }
  
  .modern-field :deep(.v-field) {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

/* Loading state */
.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Focus states for accessibility */
.modern-field :deep(.v-field--focused) {
  outline: 2px solid rgba(var(--v-theme-primary), 0.5);
  outline-offset: 2px;
}

/* Animation for form appearance */
.profile-form {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-section {
  animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  animation-fill-mode: both;
}

.form-section:nth-child(1) { animation-delay: 0.1s; }
.form-section:nth-child(2) { animation-delay: 0.2s; }
.action-section { animation-delay: 0.3s; }

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>

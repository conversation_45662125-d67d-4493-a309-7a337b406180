

import SuperTokens from "supertokens-web-js";
import Session from "supertokens-web-js/recipe/session";
import { checkProfileCompletion, getProfileCompletionRedirectUrl } from './utils/profileUtils';

const isMultitenancy = false;

export function getApiDomain() {
    const apiPort = 3001;
    const apiUrl = `http://localhost:${apiPort}`;
    return apiUrl;
}

export function getWebsiteDomain() {
    const websitePort = 3000;
    const websiteUrl = `http://localhost:${websitePort}`;
    return websiteUrl;
}

export function initSuperTokensUI() {
    (window as any).supertokensUIInit("supertokensui", {
        appInfo: {
            websiteDomain: getWebsiteDomain(),
            apiDomain: getApiDomain(),
            appName: "SuperTokens Demo App",
            websiteBasePath: "/auth",
            apiBasePath: "/auth",
        },

        recipeList: [
            (window as any).supertokensUISession.init(),
            (window as any).supertokensUIEmailPassword.init()
        ],
        getRedirectionURL: async (context: any) => {
            if (context.action === "SUCCESS") {
                // Check profile completion status and redirect accordingly
                const profileStatus = await checkProfileCompletion();
                return getProfileCompletionRedirectUrl(profileStatus);
            }
            return undefined;
        },

        onHandleEvent: async (context: any) => {
            if (context.action === "SUCCESS") {
                console.log("Auth success event:", context);

                // Small delay to ensure session is properly established
                setTimeout(async () => {
                    try {
                        if (context.isNewUser) {
                            // New user - always redirect to profile completion
                            window.location.href = "/profile-completion?mode=create";
                        } else {
                            // Existing user - check profile completion status
                            const profileStatus = await checkProfileCompletion();
                            const redirectUrl = getProfileCompletionRedirectUrl(profileStatus);

                            if (redirectUrl !== '/dashboard') {
                                window.location.href = redirectUrl;
                            }
                        }
                    } catch (error) {
                        console.error("Error in onHandleEvent:", error);
                        // Fallback to dashboard on error
                        window.location.href = "/dashboard";
                    }
                }, 100);
            }
        }
    });
}

export function initSuperTokensWebJS() {
    SuperTokens.init({
        appInfo: {
            appName: "SuperTokens Demo App",
            apiDomain: getApiDomain(),
            apiBasePath: "/auth",
        },
        recipeList: [
            Session.init()
        ]
    });

    if (isMultitenancy) {
        initTenantSelectorInterface();
    }
}

export async function initTenantSelectorInterface() { /* STUB, to prevent linters complaining */ };;
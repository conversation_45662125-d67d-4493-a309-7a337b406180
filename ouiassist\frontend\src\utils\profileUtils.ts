import { getApiDomain } from '../config';

export async function checkProfileCompletion(): Promise<{
  exists: boolean;
  isComplete: boolean;
  isMinimal: boolean;
  profile?: any;
}> {
  try {
    const response = await fetch(`${getApiDomain()}/profile/check`, {
      credentials: 'include'
    });
    
    if (response.ok) {
      return await response.json();
    }
    
    return {
      exists: false,
      isComplete: false,
      isMinimal: false
    };
  } catch (error) {
    console.error('Error checking profile completion:', error);
    return {
      exists: false,
      isComplete: false,
      isMinimal: false
    };
  }
}

export function getProfileCompletionRedirectUrl(profileStatus: {
  exists: boolean;
  isComplete: boolean;
  isMinimal: boolean;
}): string {
  if (!profileStatus.exists) {
    return '/profile-completion?mode=create';
  }
  
  if (profileStatus.isMinimal || !profileStatus.isComplete) {
    return '/profile-completion?mode=update';
  }
  
  return '/dashboard';
}

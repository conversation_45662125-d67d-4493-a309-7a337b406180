<template>
  <v-app>
    <v-container class="fill-height modern-container" fluid>
      <v-row justify="center" align="center" class="min-height-screen">
        <v-col cols="12" sm="10" md="8" lg="6" xl="5">
          <div class="profile-completion-card">
            <v-card class="elevation-24 modern-card" rounded="xl">
              <div class="card-header">
                <div class="header-content">
                  <v-icon size="48" class="header-icon mb-2">
                    {{ isUpdate ? 'mdi-account-edit' : 'mdi-account-plus' }}
                  </v-icon>
                  <h1 class="card-title">
                    {{ isUpdate ? 'Modifier votre profil' : 'Complétez votre profil' }}
                  </h1>
                  <p class="card-subtitle">
                    {{ isUpdate 
                      ? 'Mettez à jour vos informations personnelles' 
                      : 'Quelques informations pour personnaliser votre expérience' 
                    }}
                  </p>
                </div>
              </div>
              
              <v-card-text class="card-content">
                <ProfileForm 
                  :initial-data="profileData"
                  :is-update="isUpdate"
                  :loading="loading"
                  @submit="handleSubmit"
                  @skip="handleSkip"
                />
              </v-card-text>
            </v-card>
          </div>
        </v-col>
      </v-row>
    </v-container>
  </v-app>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import ProfileForm from '../components/ProfileForm.vue'
import { getApiDomain } from '../config'

const route = useRoute()
const router = useRouter()

const isUpdate = ref(false)
const profileData = ref({})
const loading = ref(false)

onMounted(async () => {
  const mode = route.query.mode as string
  isUpdate.value = mode === 'update'
  
  if (isUpdate.value) {
    await loadExistingProfile()
  }
})

async function loadExistingProfile() {
  try {
    const response = await fetch(`${getApiDomain()}/profile/check`, {
      credentials: 'include'
    })
    
    if (response.ok) {
      const data = await response.json()
      if (data.exists && data.profile) {
        profileData.value = data.profile
      }
    }
  } catch (error) {
    console.error('Error loading profile:', error)
  }
}

async function handleSubmit(formData: any) {
  loading.value = true
  
  try {
    const endpoint = isUpdate.value ? '/profile' : '/profile'
    const method = isUpdate.value ? 'PUT' : 'POST'
    
    const response = await fetch(`${getApiDomain()}${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify(formData)
    })
    
    if (response.ok) {
      router.push('/dashboard')
    } else {
      const error = await response.json()
      console.error('Profile submission error:', error)
    }
  } catch (error) {
    console.error('Network error:', error)
  } finally {
    loading.value = false
  }
}

async function handleSkip() {
  if (!isUpdate.value) {
    loading.value = true
    
    try {
      const response = await fetch(`${getApiDomain()}/profile/minimal`, {
        method: 'POST',
        credentials: 'include'
      })
      
      if (response.ok) {
        router.push('/dashboard')
      }
    } catch (error) {
      console.error('Error creating minimal profile:', error)
    } finally {
      loading.value = false
    }
  }
}
</script>

<style scoped>
.modern-container {
  background: linear-gradient(135deg, 
    rgba(var(--v-theme-primary), 0.1) 0%,
    rgba(var(--v-theme-secondary), 0.1) 100%);
  min-height: 100vh;
  padding: 2rem 1rem;
}

.min-height-screen {
  min-height: calc(100vh - 4rem);
}

.profile-completion-card {
  width: 100%;
  animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-card {
  border-radius: 24px !important;
  overflow: hidden;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
  background: linear-gradient(135deg, 
    rgb(var(--v-theme-primary)) 0%,
    rgb(var(--v-theme-secondary)) 100%);
  padding: 3rem 2rem 2rem;
  position: relative;
  overflow: hidden;
}

.card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.3;
}

.header-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.header-icon {
  color: white;
  opacity: 0.9;
  animation: pulse 2s infinite;
}

.card-title {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  font-weight: 400;
  line-height: 1.5;
  max-width: 400px;
  margin: 0 auto;
}

.card-content {
  padding: 2rem !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .modern-container {
    padding: 1rem 0.5rem;
  }
  
  .card-header {
    padding: 2rem 1.5rem 1.5rem;
  }
  
  .card-title {
    font-size: 1.6rem;
  }
  
  .card-subtitle {
    font-size: 1rem;
  }
  
  .card-content {
    padding: 1.5rem !important;
  }
}

@media (max-width: 480px) {
  .card-header {
    padding: 1.5rem 1rem;
  }
  
  .card-title {
    font-size: 1.4rem;
  }
  
  .card-content {
    padding: 1rem !important;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .modern-card {
    background: rgba(18, 18, 18, 0.95) !important;
    border-color: rgba(255, 255, 255, 0.1);
  }
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.9;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* Loading state */
.modern-card.loading {
  pointer-events: none;
  opacity: 0.8;
}

/* Focus and hover effects */
.modern-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar for the container */
.modern-container ::-webkit-scrollbar {
  width: 8px;
}

.modern-container ::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.modern-container ::-webkit-scrollbar-thumb {
  background: rgba(var(--v-theme-primary), 0.5);
  border-radius: 4px;
}

.modern-container ::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--v-theme-primary), 0.7);
}
</style>

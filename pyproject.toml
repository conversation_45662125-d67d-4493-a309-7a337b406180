[project]
name = "ouiassist"
version = "0.1.0"
description = "Digital platform connecting healthcare facilities in Senegal with qualified temporary healthcare professionals"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "flask",
    "flask-cors",
    "sqlalchemy",
    "alembic",
    "supertokens-python",
    "python-dotenv",
    "pydantic",
    "uvicorn",
    "gunicorn",
    "psycopg2-binary",
    "requests",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "pytest-cov",
    "pytest-asyncio",
    "pytest-mock",
    "factory-boy",
    "ruff",
    "mypy",
    "pre-commit",
    "types-requests",
    "types-Flask-Cors",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["core", "ouiassist", "config"]

[tool.ruff]
target-version = "py311"
line-length = 88

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["B011"]

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "supertokens_python.*",
    "factory.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests", "core/tests", "ouiassist/backend/tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=core",
    "--cov=ouiassist",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-fail-under=90",
]
markers = [
    "unit: marks tests as unit tests",
    "integration: marks tests as integration tests",
    "slow: marks tests as slow",
]

[tool.coverage.run]
source = ["core", "ouiassist"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__init__.py",
    "*/migrations/*",
]
